import { ArrowDownWideNarrowIcon, CheckIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';

const fileStatuses = [
  {
    label: 'Draft',
    value: 'draft',
  },
  {
    label: 'Staged',
    value: 'staged',
  },
  {
    label: 'Rejected',
    value: 'rejected',
  },
  {
    label: 'Approved',
    value: 'approved',
  },
  {
    label: 'Published',
    value: 'published',
  },
  {
    label: 'Deleted',
    value: 'deleted',
  },
];
const staffStatuses = [
  {
    label: 'Verified',
    value: 'verified',
  },
  {
    label: 'Unverified',
    value: 'unverified',
  },
];

interface SearchStatusProps {
  selectedStatuses: string[];
  onStatusChange: (statuses: string[]) => void;
  isStaff?: boolean;
}

export function SearchStatus({
  selectedStatuses,
  onStatusChange,
  isStaff,
}: SearchStatusProps) {
  const toggleStatus = (statusValue: string) => {
    if (selectedStatuses.includes(statusValue)) {
      // Remove status from selection
      onStatusChange(
        selectedStatuses.filter((status) => status !== statusValue)
      );
    } else {
      // Add status to selection
      onStatusChange([...selectedStatuses, statusValue]);
    }
  };

  const getActiveCount = () => {
    return selectedStatuses.length;
  };
  const statuses = isStaff ? staffStatuses : fileStatuses;
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button className="bg-background lg:flex" size="lg" variant="outline">
          <ArrowDownWideNarrowIcon className="size-5" />
          {getActiveCount() > 0 && (
            <span className="ml-1 hidden rounded-full bg-primary px-1.5 py-0.5 text-primary-foreground text-xs md:flex">
              {getActiveCount()}
            </span>
          )}
          <span className="sr-only">Search Status</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="absolute right-0 mt-0 w-48 rounded-lg p-0">
        <div className="p-2">
          <p className="font-normal text-muted-foreground text-sm">
            Filter by status
          </p>
        </div>
        <Separator />
        <div className="flex flex-col p-2">
          {statuses.map((status) => {
            const isSelected = selectedStatuses.includes(status.value);
            return (
              <Button
                className="w-full justify-start font-normal"
                key={status.label}
                onClick={() => toggleStatus(status.value)}
                variant={'ghost'}
              >
                <span>{status.label}</span>
                {isSelected && (
                  <CheckIcon className="ml-auto size-4 text-primary" />
                )}
              </Button>
            );
          })}
        </div>
        {/* <Separator className="my-2" />
        <div className="px-2">
          <Button
            className="w-full"
            onClick={() => onStatusChange(statuses.map((s) => s.value))}
            size="sm"
            variant="ghost"
          >
            Select All
          </Button>
        </div> */}
      </PopoverContent>
    </Popover>
  );
}
