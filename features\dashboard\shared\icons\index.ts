import {
  FileAudioIcon,
  FileCheck2Icon,
  FileClockIcon,
  FileIcon,
  FileImageIcon,
  FileOutputIcon,
  FilePenIcon,
  FileVideoIcon,
  FileX2Icon,
  FileXIcon,
} from "lucide-react";

type TStatus = "draft" | "staged" | "approved" | "published" | "deleted";

export const getIconByStatus = (status: TStatus | string) => {
  switch (status) {
    case "draft":
      return FilePenIcon;
    case "staged":
      return FileClockIcon;
    case "approved":
      return FileCheck2Icon;
    case "rejected":
      return FileXIcon;
    case "published":
      return FileOutputIcon;
    case "deleted":
      return FileX2Icon;
    default:
      return FileIcon;
  }
};

export const getIconByMediaType = (type: string) => {
  switch (type) {
    case "image":
      return FileImageIcon;
    case "video":
      return FileVideoIcon;
    case "audio":
      return FileAudioIcon;
    default:
      return FileIcon;
  }
};
