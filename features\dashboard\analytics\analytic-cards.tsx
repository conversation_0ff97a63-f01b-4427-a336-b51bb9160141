import { SlidingNumber } from '@/components/custom/sliding-number';
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { TPageCard } from './types';

export default function AnalyticsCards({ cards }: { cards: TPageCard[] }) {
  return (
    <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4 py-6">
      {cards.map((card) => (
        <Card
          className={cn(
            '@container/card rounded-sm bg-background shadow-sm',
            'border border-dashed transition-all duration-500 ease-in hover:border-blue-400'
          )}
          key={card.title}
        >
          <CardHeader>
            <CardDescription>{card.title}</CardDescription>
            <CardTitle className="font-semibold @[250px]/card:text-3xl text-2xl tabular-nums">
              <SlidingNumber padStart={false} value={card.value} />
            </CardTitle>
            <CardAction>
              <card.icon className="size-4" />
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="flex items-center gap-2 font-medium">
              {card.footer}
            </div>
            <div className="text-muted-foreground">{card.description}</div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
