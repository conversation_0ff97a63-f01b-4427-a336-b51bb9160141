import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import { notFound, redirect } from 'next/navigation';
import { api } from '@/convex/_generated/api';
export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await fetchQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  if (!user) {
    return redirect('/sign-in');
  }

  if (user.role !== 'admin') {
    return notFound();
  }

  return <>{children}</>;
}
