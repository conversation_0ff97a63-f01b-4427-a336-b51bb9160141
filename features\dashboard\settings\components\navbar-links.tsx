'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { settingLinks } from '@/config/dashboard';
import { cn } from '@/lib/utils';
import SearchInput from './search-input';

export function SettingLinksNavbar() {
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;
  const [searchText, setSearchText] = useState('');

  const NavsettingLinks = useMemo(() => {
    if (!searchText) return settingLinks;

    const query = searchText.toLowerCase();
    const matches = settingLinks.filter((item) =>
      item.label.toLowerCase().includes(query)
    );
    const nonMatches = settingLinks.filter(
      (item) => !item.label.toLowerCase().includes(query)
    );

    return [...matches, ...nonMatches];
  }, [searchText]);

  return (
    <div className="mr-12 hidden w-full max-w-3xs lg:block">
      <div className="sticky top-16">
        <SearchInput searchText={searchText} setSearchText={setSearchText} />

        <div className="flex flex-col">
          {NavsettingLinks.map((item) => {
            const fullHref = `${basePath}${item.href}`;
            return (
              <Button
                asChild
                className={cn(
                  'justify-start rounded-md px-4',
                  item.comingSoon && 'cursor-not-allowed '
                )}
                disabled={item.comingSoon}
                key={item.label}
                size={'lg'}
                variant={'ghost'}
              >
                {item.comingSoon ? (
                  <span className="text-muted-foreground">
                    {item.label} (Coming Soon)
                  </span>
                ) : (
                  <Link href={fullHref}>
                    <span
                      className={cn(
                        'font-normal text-muted-foreground text-sm',
                        pathname.endsWith(item.href) &&
                          'font-medium text-primary'
                      )}
                    >
                      {item.label}
                    </span>
                  </Link>
                )}
              </Button>
            );
          })}
        </div>
      </div>
    </div>
  );
}
