'use client';

import * as Ariakit from '@ariakit/react';
import * as React from 'react';
import { cn } from '@/lib/tiptap-utils';
import '@/components/tiptap-ui-primitive/combobox/combobox.scss';

export function ComboboxProvider({ ...props }: Ariakit.ComboboxProviderProps) {
  return (
    <Ariakit.ComboboxProvider
      includesBaseElement={false}
      resetValueOnHide
      {...props}
    />
  );
}

export const ComboboxList = React.forwardRef<
  React.ComponentRef<typeof Ariakit.ComboboxList>,
  React.ComponentProps<typeof Ariakit.ComboboxList>
>(({ className, ...props }, ref) => {
  return (
    <Ariakit.ComboboxList
      className={cn('tiptap-combobox-list', className)}
      ref={ref}
      {...props}
    />
  );
});
ComboboxList.displayName = 'ComboboxList';

export const ComboboxPopover = React.forwardRef<
  React.ComponentRef<typeof Ariakit.ComboboxPopover>,
  React.ComponentProps<typeof Ariakit.ComboboxPopover>
>(({ className, ...props }, ref) => {
  return (
    <Ariakit.ComboboxPopover
      className={cn('tiptap-combobox-popover', className)}
      ref={ref}
      {...props}
    />
  );
});
ComboboxPopover.displayName = 'ComboboxPopover';

export const Combobox = React.forwardRef<
  React.ComponentRef<typeof Ariakit.Combobox>,
  React.ComponentProps<typeof Ariakit.Combobox>
>(({ className, ...props }, ref) => {
  return (
    <Ariakit.Combobox
      autoSelect
      ref={ref}
      {...props}
      className={cn('tiptap-combobox', className)}
    />
  );
});
Combobox.displayName = 'Combobox';

export const ComboboxItem = React.forwardRef<
  React.ComponentRef<typeof Ariakit.ComboboxItem>,
  React.ComponentProps<typeof Ariakit.ComboboxItem>
>(({ className, ...props }, ref) => {
  return (
    <Ariakit.ComboboxItem
      className={cn('tiptap-combobox-item', className)}
      ref={ref}
      {...props}
    />
  );
});
ComboboxItem.displayName = 'ComboboxItem';
