'use client';
import type { FunctionReturnType } from 'convex/server';
import { format } from 'date-fns';
import { MessageCircle } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import type { api } from '@/convex/_generated/api';
import profileImg from '@/public/profile.svg';

type TConversation = FunctionReturnType<typeof api.chat.getConversations>;
export function InboxTab({
  unreadConversations,
}: {
  unreadConversations: TConversation | null;
}) {
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;
  const chatPath = `${basePath}/chat`;
  if (!unreadConversations) {
    return null;
  }

  // get conversations with unread messages

  if (unreadConversations.length === 0) {
    return (
      <div className="h-full w-full py-1">
        <div className="flex h-full min-h-100 w-full flex-col items-center justify-center gap-2">
          <div className="flex items-center justify-center rounded-full bg-muted/50 p-2">
            <MessageCircle className="size-6 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground text-sm">No new messages</p>
        </div>
      </div>
    );
  }
  return (
    <div className="h-full w-full py-1">
      <div className="flex h-full min-h-100 w-full flex-col">
        {unreadConversations.map((conversation) => (
          <div key={conversation.otherUser._id}>
            <Button
              className="flex h-fit w-full items-center justify-between gap-1 rounded-none"
              variant={'ghost'}
            >
              <Link
                className="flex w-full items-center justify-between gap-1"
                href={`${chatPath}?username=${conversation.otherUser.username}`}
              >
                <div className="flex items-center gap-1">
                  <Avatar className="size-10">
                    <AvatarImage
                      src={conversation.otherUser.avatarUrl || profileImg.src}
                    />
                    <AvatarFallback>
                      {conversation.otherUser.name?.charAt(0) ??
                        conversation.otherUser.username?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <p className="text-muted-foreground tracking-tight">
                    New message from{' '}
                    <span className="font-medium text-primary">
                      {conversation.otherUser.username}
                    </span>{' '}
                    <Badge className="size-4 min-w-4 rounded-full px-1 font-mono tabular-nums">
                      {conversation.unreadCount}
                    </Badge>{' '}
                  </p>
                </div>
                <p className="ml-auto font-normal text-muted-foreground text-sm">
                  {format(
                    new Date(conversation?.lastMessage?._creationTime || 0),
                    'hh:mm a'
                  )}
                </p>
              </Link>
            </Button>
            <Separator />
          </div>
        ))}
        {/* <Button
          className="flex h-fit w-full items-center justify-between gap-1 rounded-none"
          variant={'ghost'}
        >
          <Link
            className="flex w-full items-center justify-between gap-1"
            href="/dashboard/notification/chat"
          >
            <div className="flex items-center gap-1">
              <Avatar className="size-10">
                <AvatarImage src={profileImg.src} />
                <AvatarFallback>l</AvatarFallback>
              </Avatar>
              <p className="text-muted-foreground tracking-tight">
                New message from{' '}
                <span className="font-medium text-primary">Lecon</span>{' '}
                <Badge className="size-4 min-w-4 rounded-full px-1 font-mono tabular-nums">
                  3
                </Badge>{' '}
              </p>
            </div>

            <p className="ml-auto font-normal text-muted-foreground text-sm">
              11:08 AM
            </p>
          </Link>
        </Button> */}
      </div>
    </div>
  );
}
