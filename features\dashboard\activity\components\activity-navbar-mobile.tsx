'use client';
import { MenuIcon } from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

const activityTypes = [
  { id: 'all', label: 'All' },
  { id: 'created', label: 'Created' },
  { id: 'updated', label: 'Updated' },
  { id: 'added-to-favorite', label: 'Added to Favorite' },
  { id: 'removed-from-favorite', label: 'Removed from Favorite' },
  { id: 'deleted', label: 'Deleted' },
  { id: 'restored', label: 'Restored' },
  { id: 'staged', label: 'Staged' },
  { id: 'unstaged', label: 'Unstaged' },
];
export function ActivityNavbarMobile() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [checkedTypes, setCheckedTypes] = useState<string[]>([]);
  const [open, setOpen] = useState(false);

  // Initialize state from URL
  useEffect(() => {
    const types = searchParams.getAll('type');
    setCheckedTypes(types.length ? types : ['all']);
  }, [searchParams]);

  const updateUrl = (types: string[]) => {
    const params = new URLSearchParams();
    for (const t of types) {
      params.append('type', t);
    }
    router.push(`${pathname}${types.length ? `?${params}` : ''}`);
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    let newTypes: string[] = [];

    if (id === 'all') {
      newTypes = checked ? ['all'] : [];
    } else if (checked) {
      // Remove "all" if it exists
      newTypes = [...checkedTypes.filter((t) => t !== 'all'), id];
    } else {
      newTypes = checkedTypes.filter((t) => t !== id);
    }

    setOpen(false);
    setCheckedTypes(newTypes);
    updateUrl(newTypes);
  };
  return (
    <Drawer onOpenChange={setOpen} open={open}>
      <DrawerTrigger asChild>
        <Button
          className="bg-background lg:hidden"
          size={'icon'}
          variant={'outline'}
        >
          <MenuIcon className="size-5" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="min-h-52">
        <DrawerHeader className="sr-only">
          <DrawerTitle>Activity</DrawerTitle>
          <DrawerDescription>Activity navigation links.</DrawerDescription>
        </DrawerHeader>
        <div className="p-6">
          <div className="mt-10 flex flex-col gap-5">
            {activityTypes.map((type) => (
              <div key={type.id}>
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={checkedTypes.includes(type.id)}
                    className="cursor-pointer"
                    id={type.id}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(type.id, Boolean(checked))
                    }
                  />
                  <Label
                    // if checked text-primary else text-muted-foreground
                    className={cn(
                      'cursor-pointer font-normal',
                      checkedTypes.includes(type.id)
                        ? 'text-primary'
                        : 'text-muted-foreground'
                    )}
                    htmlFor={type.id}
                  >
                    {type.label}
                  </Label>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
