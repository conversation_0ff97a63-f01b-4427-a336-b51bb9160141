import z from 'zod';
export const articleEditorFormSchema = z.object({
  title: z
    .string()
    .min(3, { message: 'Title must be at least 3 characters.' })
    .max(160, { message: 'Title must not be longer than 160 characters.' }),
  description: z.string().optional(),
  content: z.string().optional(),
  words: z.number().optional(),
});
export type TArticleEditorFormSchema = z.infer<typeof articleEditorFormSchema>;
