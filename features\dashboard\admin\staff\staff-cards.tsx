'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { useEffect, useMemo, useState } from 'react';
import { api } from '@/convex/_generated/api';
import ViewToggle from '@/features/dashboard/shared/components/view-toggle';
import { SearchStatus } from '@/features/dashboard/shared/search/search-status';
import SearchTop from '@/features/dashboard/shared/search/search-top';

import StaffItemCard from './staff-card';
import StaffTable from './staff-table';

export default function StaffCards() {
  const [searchText, setSearchText] = useState('');
  const [view, setView] = useState<'grid' | 'table'>('grid');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'verified',
    'unverified',
  ]);

  const allStaff = useQuery(api.admin.getStaffMembers) || [];

  const searchResults =
    useQuery(api.admin.searchStaffMembers, {
      query: searchText,
    }) || [];

  useEffect(() => {
    const storedView = localStorage.getItem('articleView');
    if (storedView === 'grid' || storedView === 'table') {
      setView(storedView);
    } else {
      localStorage.setItem('articleView', 'grid');
    }
  }, []);

  const filteredArticles = useMemo(() => {
    if (!(allStaff && Array.isArray(allStaff))) {
      return [];
    }

    return allStaff.filter((staffMember) =>
      selectedStatuses.includes(
        staffMember.verified ? 'verified' : 'unverified'
      )
    );
  }, [allStaff, selectedStatuses]);

  const handleStatusChange = (statuses: string[]) => {
    setSelectedStatuses(statuses);
  };

  const staffMembers = useMemo(() => {
    return searchText ? searchResults : filteredArticles;
  }, [searchText, searchResults, filteredArticles]);

  if (!(staffMembers && Array.isArray(staffMembers))) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between gap-2">
        <SearchTop
          searchPlaceholder="Search Staff Members..."
          searchText={searchText}
          setSearchText={setSearchText}
        />
        <SearchStatus
          isStaff
          onStatusChange={handleStatusChange}
          selectedStatuses={selectedStatuses}
        />
        <ViewToggle setView={setView} view={view} />
      </div>
      {view === 'grid' ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {staffMembers.map((staffMember) => (
            <StaffItemCard key={staffMember._id} staff={staffMember} />
          ))}
          {staffMembers.length === 0 && (
            <div className="col-span-full flex flex-col items-center justify-center gap-2">
              <p className="text-muted-foreground text-sm">
                No staff members found.
              </p>
            </div>
          )}
        </div>
      ) : (
        <StaffTable staffMembers={staffMembers} />
      )}
    </>
  );
}
