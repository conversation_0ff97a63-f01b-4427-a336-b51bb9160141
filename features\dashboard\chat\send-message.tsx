'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';

const FormSchema = z.object({
  content: z.string().min(2, {
    message: 'Message must be at least 2 characters.',
  }),
});

export function SendMessageForm({
  receiverId,
  receiverName,
}: {
  receiverId: Id<'users'>;
  receiverName: string;
}) {
  const sendMessage = useMutation(api.chat.sendMessage);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zod<PERSON>esolver(FormSchema),
    defaultValues: {
      content: '',
    },
    mode: 'onBlur',
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    try {
      sendMessage({ receiverId, receiverName, content: data.content });
      form.reset();
      toast.success('Message sent!');
    } catch {
      toast.error('Failed to send message.');
    }
  }

  return (
    <Form {...form}>
      <form
        className="flex flex-1 gap-4 px-6"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel className="sr-only">Message</FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  className="text-primary/80"
                  placeholder="Type your message..."
                  spellCheck={false}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Send</Button>
      </form>
    </Form>
  );
}
