import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery, preloadQuery } from 'convex/nextjs';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import MediaDetails from '@/features/dashboard/media/media-details';

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const media = await fetchQuery(
    api.media.getMediaFileBySlug,
    { slug },
    { token: await convexAuthNextjsToken() }
  );
  if (!media || 'success' in media) {
    return notFound();
  }
  return {
    title: `Media - ${media.title}`,
    description: media.description,
  };
}

type Props = {
  params: Promise<{ slug: string }>;
};

export default async function MediaPage({ params }: Props) {
  const { slug } = await params;
  const media = await preloadQuery(
    api.media.getMediaFileBySlug,
    { slug },
    { token: await convexAuthNextjsToken() }
  );
  if (!media || 'success' in media) {
    return notFound();
  }

  return <MediaDetails preloadedMedia={media} />;
}
