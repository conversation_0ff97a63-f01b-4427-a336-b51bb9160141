'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { InfoIcon, PlusIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import z from 'zod';
import { Button, buttonVariants } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { api } from '@/convex/_generated/api';
import { cn } from '@/lib/utils';

const emailSchema = z.object({
  email: z.email().min(1, 'Email is required'),
  terms: z.boolean().refine((val) => val, {
    message: 'You must agree to the terms and conditions.',
  }),
});
type TEmailSchema = z.infer<typeof emailSchema>;
export default function AddNewEmail() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const addOtherEmail = useMutation(api.users.addUserEmail);

  const form = useForm<TEmailSchema>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: '', terms: false },
    mode: 'onBlur',
  });
  const handleSubmit = (values: TEmailSchema) => {
    setIsSubmitting(true);
    setIsOpen(false);

    try {
      addOtherEmail({ email: values.email, isVerified: false });
      form.reset();
      toast.success('Email added successfully!');
    } catch {
      toast.error('Failed to add email.');
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Dialog onOpenChange={setIsOpen} open={isOpen}>
      <DialogTrigger
        className={cn(
          buttonVariants({ variant: 'outline', size: 'lg' }),
          'w-fit cursor-pointer rounded-sm px-3 dark:bg-transparent'
        )}
      >
        <PlusIcon />
        Add Another
      </DialogTrigger>

      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader>
          <DialogTitle>Add Email</DialogTitle>
          <DialogDescription>
            Add a new email address to your account. This email, once verified,
            can be used to login to your account.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Email" {...field} />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="terms"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center gap-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal text-sm">
                      I understand that this email will have access to my
                      account
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <InfoIcon className="size-4 cursor-pointer text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-60 text-center">
                          <p>
                            Anyone have access to this email will be able to
                            login to your account and access all of your
                            resources.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </FormLabel>
                  </div>

                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" disabled={isSubmitting} type="submit">
              Add Email
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
