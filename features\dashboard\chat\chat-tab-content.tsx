'use client';

import { useMutation, useQuery } from 'convex/react';
import type { FunctionReturnType } from 'convex/server';
import { format } from 'date-fns';
import { BriefcaseBusinessIcon } from 'lucide-react';
import { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { api } from '@/convex/_generated/api';
import profileImg from '@/public/profile.svg';
import ChatMessages from './chat-messages';
import { SendMessageForm } from './send-message';

type Tuser = FunctionReturnType<typeof api.users.getUser>;

export default function ChatTabContent({
  otherUser,
}: {
  otherUser: Tuser | null;
}) {
  const currentUser = useQuery(api.users.getUser);
  const messages = useQuery(
    api.chat.getConversation,
    otherUser ? { otherUserId: otherUser._id } : 'skip'
  );

  const markAsRead = useMutation(api.chat.markAsRead);

  useEffect(() => {
    if (otherUser?._id) {
      markAsRead({ otherUserId: otherUser._id });
    }
  }, [otherUser?._id, markAsRead]);

  if (!(otherUser && currentUser) || messages === undefined) {
    return null;
  }

  // Handle loading state
  if (messages === undefined) {
    return <p className="p-4 text-muted-foreground">Loading messages...</p>;
  }

  return (
    <div className="flex h-screen w-full flex-col gap-2">
      <HoverCard>
        <HoverCardTrigger asChild>
          <Avatar className="sticky top-12 z-10 mx-2 my-3 size-10">
            <AvatarImage src={otherUser.avatarUrl || profileImg.src} />
            <AvatarFallback>
              {otherUser.name?.charAt(0) ?? otherUser.username?.charAt(0)}
            </AvatarFallback>
          </Avatar>
        </HoverCardTrigger>
        <HoverCardContent className="w-fit min-w-72">
          <div className="flex gap-4">
            <Avatar>
              <AvatarImage src={otherUser.avatarUrl || profileImg.src} />
              <AvatarFallback>
                {otherUser.name?.charAt(0) ?? otherUser.username?.charAt(0)}
              </AvatarFallback>
            </Avatar>

            <div className="space-y-1">
              <div className="flex justify-between gap-1">
                <h4 className="font-semibold text-sm">
                  @ {otherUser.username}
                </h4>
                <div className="flex items-center gap-1">
                  <BriefcaseBusinessIcon className="size-4 text-muted-foreground" />
                  <p className="text-muted-foreground text-sm">
                    {otherUser.role}
                  </p>
                </div>
              </div>
              <p className="line-clamp-2 max-w-60 truncate text-muted-foreground text-sm">
                {otherUser.bio}
              </p>
              <div className=" text-xs">
                Joined on {format(new Date(otherUser._creationTime), 'PP')}
              </div>
            </div>
          </div>
        </HoverCardContent>
      </HoverCard>

      <ScrollArea className="z-0 h-[calc(100vh-10.1rem)] w-full px-6">
        <ChatMessages currentUserId={currentUser._id} messages={messages} />
        <ScrollBar className="h-0 w-0" orientation="horizontal" />
      </ScrollArea>

      <div className="mt-auto w-full py-2">
        <SendMessageForm
          receiverId={otherUser._id}
          receiverName={otherUser.name || otherUser.username || 'Unknown'}
        />
      </div>
    </div>
  );
}
