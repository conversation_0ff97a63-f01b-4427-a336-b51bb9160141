import { DataTable } from '@/components/custom/data-table';
import type { TStaff } from './staff-card';
import { staffColumns } from './staff-column';

export default function StaffTable({
  staffMembers,
}: {
  staffMembers: TStaff[];
}) {
  if (!(staffMembers && Array.isArray(staffMembers))) {
    return null;
  }
  // Handle empty staff state
  if (staffMembers.length === 0) {
    return (
      <div className="p-4 text-muted-foreground">
        No staff members found. Create your first staff member!
      </div>
    );
  }

  return (
    <div className=" rounded-md bg-background p-4">
      <DataTable columns={staffColumns} data={staffMembers} />
    </div>
  );
}
