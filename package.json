{"name": "better", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "build": "next build", "start": "next start", "lint": "next lint", "biome-lint": "npx ultracite lint", "format": "npx ultracite format"}, "dependencies": {"@auth/core": "0.37.0", "@bprogress/next": "^3.2.12", "@convex-dev/auth": "^0.0.87", "@convex-dev/r2": "^0.7.1", "@convex-dev/resend": "^0.1.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.15", "@hookform/resolvers": "^5.2.1", "@oslojs/crypto": "^1.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.5.0", "@react-email/render": "^1.2.0", "@tanstack/react-table": "^8.21.3", "@tiptap/core": "^3.2.0", "@tiptap/extension-color": "^3.2.0", "@tiptap/extension-drag-handle-react": "^3.2.0", "@tiptap/extension-emoji": "^3.2.0", "@tiptap/extension-highlight": "^3.2.0", "@tiptap/extension-horizontal-rule": "^3.2.0", "@tiptap/extension-image": "^3.2.0", "@tiptap/extension-list": "^3.2.0", "@tiptap/extension-subscript": "^3.2.0", "@tiptap/extension-superscript": "^3.2.0", "@tiptap/extension-text-align": "^3.2.0", "@tiptap/extension-text-style": "^3.2.0", "@tiptap/extension-typography": "^3.2.0", "@tiptap/extension-unique-id": "^3.2.0", "@tiptap/extensions": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "@tiptap/suggestion": "^3.2.0", "@types/canvas-confetti": "^1.9.0", "@xixixao/uploadstuff": "^0.0.5", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.25.4", "convex-helpers": "^0.1.100", "date-fns": "^4.1.0", "geist": "^1.4.2", "input-otp": "^1.4.2", "lodash.throttle": "^4.1.1", "lucide-react": "^0.534.0", "motion": "^12.23.12", "next": "15.4.5", "next-themes": "^0.4.6", "next-video": "^2.2.3", "radix-ui": "^1.4.3", "react": "19.1.0", "react-dom": "19.1.0", "react-email": "^4.2.8", "react-hook-form": "^7.61.1", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.12", "react-textarea-autosize": "^8.5.9", "react-use-measure": "^2.1.7", "recharts": "2.15.4", "resend": "^6.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "wavesurfer.js": "^7.10.1", "zod": "^4.0.14"}, "devDependencies": {"@ariakit/react": "^0.4.18", "@biomejs/biome": "2.1.2", "@tailwindcss/postcss": "^4", "@types/lodash.throttle": "^4.1.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "npm-run-all": "^4.1.5", "sass": "^1.90.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "ultracite": "5.1.2"}}