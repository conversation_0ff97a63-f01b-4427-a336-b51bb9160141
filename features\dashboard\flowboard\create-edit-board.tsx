// 'use client';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { useMutation } from 'convex/react';
// import { useEffect, useState } from 'react';
// import { useForm } from 'react-hook-form';
// import { toast } from 'sonner';
// import { Spinner } from '@/components/custom/spinner';
// import { Button, buttonVariants } from '@/components/ui/button';
// import {
//   Dialog,
//   DialogClose,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from '@/components/ui/dialog';
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from '@/components/ui/form';
// import { Input } from '@/components/ui/input';
// import { api } from '@/convex/_generated/api';
// import type { Id } from '@/convex/_generated/dataModel';
// import { cn } from '@/lib/utils';
// import { boardSchema, type TBoardSchema } from './schema';

// interface CreateOrUpdateBoardProps {
//   openDialog?: boolean;
//   setOpenDialog?: (open: boolean) => void;
//   name?: string;
//   id?: Id<'boards'>;
// }

// export default function CreateOrUpdateBoard({
//   openDialog,
//   setOpenDialog,
//   name,
//   id,
// }: CreateOrUpdateBoardProps) {
//   const isEditMode = Boolean(id);
//   const createBoardMutation = useMutation(api.board.createBoard);
//   const updateBoardMutation = useMutation(api.board.updateBoard);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [internalOpen, setInternalOpen] = useState(false);

//   const open = openDialog ?? internalOpen;
//   const setOpen = setOpenDialog ?? setInternalOpen;

//   const form = useForm<TBoardSchema>({
//     resolver: zodResolver(boardSchema),
//     defaultValues: {
//       name: name || '',
//     },
//   });
//   useEffect(() => {
//     if (open) {
//       form.reset({
//         name: name || '',
//       });
//     }
//   }, [open, name, form]);

//   const handleSubmit = async (data: TBoardSchema) => {
//     setIsSubmitting(true);
//     setOpen(false);

//     try {
//       if (isEditMode) {
//         if (!id) {
//           toast.error('Failed to get board id.');
//           return;
//         }
//         const result = await updateBoardMutation({
//           id,
//           ...data,
//         });
//         if (result?.success) {
//           form.reset();
//           toast.success('Board updated successfully!');
//         } else {
//           toast.error(result.error);
//         }
//       } else {
//         const result = await createBoardMutation(data);
//         if (result?.success) {
//           form.reset();
//           toast.success('Board created successfully!');
//         } else {
//           toast.error(result.error);
//         }
//       }
//     } catch {
//       toast.error('Something went wrong.');
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   return (
//     <Dialog onOpenChange={setOpen} open={open}>
//       {!isEditMode && (
//         <DialogTrigger
//           className={cn(
//             buttonVariants({ variant: 'default', size: 'lg' }),
//             'cursor-pointer rounded-sm px-3'
//           )}
//         >
//           Add New Board
//         </DialogTrigger>
//       )}

//       <DialogContent className="flex w-full max-w-md flex-col">
//         <DialogHeader className="sr-only">
//           <DialogTitle>
//             {isEditMode ? 'Update' : 'Create New'} Board
//           </DialogTitle>
//         </DialogHeader>
//         <Form {...form}>
//           <form
//             className="mt-6 space-y-4"
//             onSubmit={form.handleSubmit(handleSubmit)}
//           >
//             <FormField
//               control={form.control}
//               name="name"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Name</FormLabel>
//                   <FormControl>
//                     <Input placeholder="Name" {...field} />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             <Button className="w-full" disabled={isSubmitting} type="submit">
//               {isSubmitting ? (
//                 <Spinner text="Submitting..." />
//               ) : isEditMode ? (
//                 'Update Board'
//               ) : (
//                 'Create Board'
//               )}
//             </Button>
//           </form>
//         </Form>
//         <DialogClose />
//       </DialogContent>
//     </Dialog>
//   );
// }
