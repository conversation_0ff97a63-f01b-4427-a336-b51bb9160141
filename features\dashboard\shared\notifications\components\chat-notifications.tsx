'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { BellIcon } from 'lucide-react';
import { TabsView } from '@/components/custom/tabs-view';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import { cn } from '@/lib/utils';
import { ActivityTab } from './activity-tab';
import { InboxTab } from './inbox-tab';

export function ChatNotifications() {
  const conversations = useQuery(api.chat.getConversations);
  // i want to show placeholder when loading and no data
  if (!conversations || conversations === undefined) {
    return (
      <Button
        className={cn(
          'relative size-8 rounded-full bg-transparent text-muted-foreground hover:text-primary'
        )}
        size={'icon'}
        variant="outline"
      >
        <BellIcon className="size-5" />
        <span className="sr-only">Open chat</span>
      </Button>
    );
  }

  const unreadConversations = conversations.filter(
    (conversation) => conversation.unreadCount > 0
  );
  const tabs = [
    {
      value: 'inbox',
      label: 'Inbox',
      component: <InboxTab unreadConversations={unreadConversations} />,
    },

    {
      value: 'activity',
      label: 'Activity',
      component: <ActivityTab />,
    },
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            'relative size-8 rounded-full bg-transparent text-muted-foreground hover:text-primary',
            unreadConversations.length > 0 && 'text-primary'
          )}
          size={'icon'}
          variant="outline"
        >
          <BellIcon className="size-5" />
          <span className="sr-only">Open chat</span>
          {unreadConversations.length > 0 && (
            <div className="-end-1 absolute top-0 right-0 flex size-2 animate-bounce">
              <span
                className={cn(
                  'absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-500 opacity-75'
                )}
              />
              <span
                className={cn(
                  'relative inline-flex size-2 rounded-full bg-blue-500'
                )}
              />
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="mt-4 mr-4 min-h-125 min-w-fit rounded-sm bg-background p-0 md:absolute md:right-4 md:mr-0 md:min-w-105">
        <TabsView
          className="gap-1"
          defaultValue="inbox"
          tabContentClassName=""
          tablistClassName="mt-3"
          tabs={tabs}
        />
      </PopoverContent>
    </Popover>
  );
}
