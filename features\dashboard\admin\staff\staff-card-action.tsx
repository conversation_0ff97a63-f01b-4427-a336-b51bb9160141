import { type ReactMutation, useMutation, useQuery } from 'convex/react';
import type { FunctionReference } from 'convex/server';
import { EllipsisIcon } from 'lucide-react';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import type { TStaff } from './staff-card';
export default function StaffCardAction({ staff }: { staff: TStaff }) {
  const user = useQuery(api.users.getUser);
  const verifyStaffMember = useMutation(api.admin.verifyStaffMember);
  const unverifyStaffMember = useMutation(api.admin.unverifyStaffMember);
  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            id: Id<'users'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { id: Id<'users'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    []
  );
  if (!staff || 'success' in staff) {
    return null;
  }
  if (!user || 'success' in user) {
    return null;
  }
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button size="icon" variant="ghost">
          <EllipsisIcon />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="absolute right-4 w-56 p-2">
        <div className="flex flex-col">
          {staff.verified ? (
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  unverifyStaffMember,
                  { id: staff._id },
                  'Staff unverified successfully!',
                  'Failed to unverify staff.'
                )
              }
              variant="ghost"
            >
              <span>Unverify</span>
            </Button>
          ) : (
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  verifyStaffMember,
                  { id: staff._id },
                  'Staff verified successfully!',
                  'Failed to verify staff.'
                )
              }
              variant="ghost"
            >
              <span>Verify</span>
            </Button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
