'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { mediaSchema, type TMediaSchema } from './schema';

interface CreateOrUpdateArticleProps {
  openDialog: boolean;
  setOpenDialog: (open: boolean) => void;
  title: string;
  url: string;
  id: Id<'mediaFiles'>;
}

export default function UpdateMedia({
  openDialog,
  setOpenDialog,
  title,
  url,
  id,
}: CreateOrUpdateArticleProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);
  const updateMediaMutation = useMutation(api.media.updateMediaFile);

  const open = openDialog ?? internalOpen;
  const setOpen = setOpenDialog ?? setInternalOpen;

  const form = useForm<TMediaSchema>({
    resolver: zodResolver(mediaSchema),
    defaultValues: {
      title: title || '',
      url: url || '',
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        title: title || '',
        url: url || '',
      });
    }
  }, [open, title, url, form]);

  const handleSubmit = async (data: TMediaSchema) => {
    setIsSubmitting(true);
    setOpen(false);

    try {
      if (!id) {
        toast.error('Failed to get media id.');
        return;
      }
      const result = await updateMediaMutation({
        id,
        title: data.title,
      });
      if (result?.success) {
        form.reset();
        toast.success('Media updated successfully!');
      } else {
        toast.error(result.error);
      }
    } catch {
      toast.error('Something went wrong.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>Update Media</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Title" {...field} />
                  </FormControl>
                  <FormDescription>
                    The title that identifies your media.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL</FormLabel>
                  <FormControl>
                    <Input placeholder="URL" type="url" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" disabled={isSubmitting} type="submit">
              {isSubmitting ? <Spinner text="Submitting..." /> : 'Update Media'}
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
