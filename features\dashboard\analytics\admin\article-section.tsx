'use client';

import { ChartSection } from '../chart-section';
import AdminArticleAnalyticsCards from './admin-article-cards';
import { useArticlesAnalyticsChartDataByAdmin } from './data/articles';

export default function ArticleAnalyticsSection() {
  const articlesChartData = useArticlesAnalyticsChartDataByAdmin();
  return (
    <main className="@container/main container mx-auto min-h-screen px-4 py-6">
      <AdminArticleAnalyticsCards />
      <ChartSection
        chartData={articlesChartData}
        description="Articles analytics"
        title="Articles"
      />
    </main>
  );
}
