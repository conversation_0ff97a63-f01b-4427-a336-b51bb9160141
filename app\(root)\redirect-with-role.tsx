'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Loader } from '@/components/custom/loader';
import { TextShimmerWave } from '@/components/custom/text-shimmer-wave';

interface RedirectWithRoleProps {
  role?: 'author' | 'admin' | 'media-manager' | 'ads-manager' | null | string;
  status?: 'unauthenticated' | 'authenticated' | null | string;
}

export default function RedirectWithRole({
  role,
  status,
}: RedirectWithRoleProps) {
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.replace('/sign-in');
    } else {
      switch (role) {
        case 'author':
          router.replace('/author');
          break;
        case 'admin':
          router.replace('/admin');
          break;
        case 'media-manager':
          router.replace('/media-manager');
          break;
        case 'ads-manager':
          router.replace('/ads');
          break;
        default:
          router.replace('/');
      }
    }
  }, [role, status, router]);

  return (
    <div className="flex h-screen flex-col items-center justify-center gap-6">
      <Loader size={30} />
      <TextShimmerWave className="font-mono text-sm" duration={1}>
        Redirecting to your dashboard...
      </TextShimmerWave>
    </div>
  );
}
