/** biome-ignore-all lint/complexity/noExcessiveCognitiveComplexity: <explanation> */
'use client';

import { useUploadFile } from '@convex-dev/r2/react';
import {
  CloudUpload,
  FileArchiveIcon,
  FileSpreadsheetIcon,
  FileTextIcon,
  HeadphonesIcon,
  ImageIcon,
  LoaderIcon,
  RefreshCwIcon,
  Trash2,
  TriangleAlert,
  VideoIcon,
} from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import {
  Alert,
  AlertContent,
  AlertDescription,
  AlertIcon,
  AlertTitle,
} from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { api } from '@/convex/_generated/api';
import {
  type FileMetadata,
  type FileWithPreview,
  formatBytes,
  useFileUpload,
} from '@/hooks/use-file-upload';
import { cn } from '@/lib/utils';
import { Spinner } from './custom/spinner';
import { UploadIllustration } from './custom/upload-illustartion';
import { AspectRatio } from './ui/aspect-ratio';

interface FileUploadItem extends FileWithPreview {
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

interface TableUploadProps {
  maxFiles?: number;
  maxSize?: number;
  accept?: string;
  multiple?: boolean;
  className?: string;
  onFilesChange?: (files: FileWithPreview[]) => void;
  simulateUpload?: boolean;
}

export default function TableUpload({
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024, // 50MB
  accept = '*',
  multiple = true,
  className,
  onFilesChange,
  simulateUpload = true,
}: TableUploadProps) {
  const uploadFileToCloudflare = useUploadFile(api.media);
  const [isUploading, setIsUploading] = useState(false);
  // Create default files using FileMetadata type
  const defaultFiles: FileMetadata[] = [];

  // Convert default files to FileUploadItem format
  const defaultUploadFiles: FileUploadItem[] = defaultFiles.map((file) => ({
    id: file.id,
    file: {
      name: file.name,
      size: file.size,
      type: file.type,
    } as File,
    preview: file.url,
    progress: 100,
    status: 'completed' as const,
  }));

  const [uploadFiles, setUploadFiles] =
    useState<FileUploadItem[]>(defaultUploadFiles);

  const [
    { isDragging, errors },
    {
      removeFile,
      clearFiles,
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      getInputProps,
    },
  ] = useFileUpload({
    maxFiles,
    maxSize,
    accept,
    multiple,
    initialFiles: defaultFiles,
    onFilesChange: (newFiles) => {
      // Convert to upload items when files change, preserving existing status
      const newUploadFiles = newFiles.map((file) => {
        // Check if this file already exists in uploadFiles
        const existingFile = uploadFiles.find(
          (existing) => existing.id === file.id
        );

        if (existingFile) {
          // Preserve existing file status and progress
          return {
            ...existingFile,
            ...file, // Update any changed properties from the file
          };
        }
        return {
          ...file,
          progress: 0,
          status: 'uploading' as const,
        };
      });
      setUploadFiles(newUploadFiles);
      onFilesChange?.(newFiles);
    },
  });

  // Simulate upload progress
  useEffect(() => {
    if (!simulateUpload) return;

    const interval = setInterval(() => {
      setUploadFiles((prev) =>
        prev.map((file) => {
          if (file.status !== 'uploading') return file;

          const increment = Math.random() * 15 + 5; // 5-20% increment
          const newProgress = Math.min(file.progress + increment, 100);

          if (newProgress >= 100) {
            // Randomly decide if upload succeeds or fails
            const shouldFail = Math.random() < 0.1; // 10% chance to fail
            return {
              ...file,
              progress: 100,
              status: shouldFail ? ('error' as const) : ('completed' as const),
              error: shouldFail
                ? 'Upload failed. Please try again.'
                : undefined,
            };
          }

          return { ...file, progress: newProgress };
        })
      );
    }, 500);

    return () => clearInterval(interval);
  }, [simulateUpload]);

  const removeUploadFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((file) => file.id !== fileId));
    removeFile(fileId);
  };

  const retryUpload = (fileId: string) => {
    setUploadFiles((prev) =>
      prev.map((file) =>
        file.id === fileId
          ? {
              ...file,
              progress: 0,
              status: 'uploading' as const,
              error: undefined,
            }
          : file
      )
    );
  };

  const getFileIcon = (file: File | FileMetadata) => {
    const type = file instanceof File ? file.type : file.type;
    if (type.startsWith('image/')) return <ImageIcon className="size-4" />;
    if (type.startsWith('video/')) return <VideoIcon className="size-4" />;
    if (type.startsWith('audio/')) return <HeadphonesIcon className="size-4" />;
    if (type.includes('pdf')) return <FileTextIcon className="size-4" />;
    if (type.includes('word') || type.includes('doc'))
      return <FileTextIcon className="size-4" />;
    if (type.includes('excel') || type.includes('sheet'))
      return <FileSpreadsheetIcon className="size-4" />;
    if (type.includes('zip') || type.includes('rar'))
      return <FileArchiveIcon className="size-4" />;
    return <FileTextIcon className="size-4" />;
  };

  const getFileTypeLabel = (file: File | FileMetadata) => {
    const type = file instanceof File ? file.type : file.type;
    if (type.startsWith('image/')) return 'Image';
    if (type.startsWith('video/')) return 'Video';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.includes('pdf')) return 'PDF';
    if (type.includes('word') || type.includes('doc')) return 'Word';
    if (type.includes('excel') || type.includes('sheet')) return 'Excel';
    if (type.includes('zip') || type.includes('rar')) return 'Archive';
    if (type.includes('json')) return 'JSON';
    if (type.includes('text')) return 'Text';
    return 'File';
  };
  const uploadAllFiles = async () => {
    try {
      const uploadPromises = uploadFiles
        .filter((fileItem) => fileItem.status === 'completed')
        .map((fileItem) =>
          handleFileUpload(fileItem.file as File, fileItem.id)
        );
      await Promise.all(uploadPromises);
      clearFiles();
      toast.success('All files uploaded successfully!');
    } catch {
      toast.error('Failed to upload files.');
    }
  };
  const handleFileUpload = async (file: File, id: string) => {
    try {
      setIsUploading(true);
      await uploadFileToCloudflare(file);
      removeFile(id);
      toast.success('File uploaded successfully!');
    } catch {
      toast.error('Failed to upload file.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div
      className={cn(
        'flex min-h-[calc(100vh-10rem)] w-full flex-col items-center justify-center gap-4',
        className
      )}
    >
      {/* Upload Area */}
      {/** biome-ignore lint/a11y/useSemanticElements: <explanation> */}
      {/** biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
      {/** biome-ignore lint/a11y/noStaticElementInteractions: <explanation> */}
      {/** biome-ignore lint/nursery/noNoninteractiveElementInteractions: <explanation> */}
      <div
        aria-label="Drop files here or click to browse"
        className={cn(
          'relative rounded-lg border border-dashed bg-background p-6 py-11 text-center transition-colors lg:min-w-xl',
          isDragging
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50'
        )}
        onClick={openFileDialog}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        role="button"
        tabIndex={0}
      >
        <input {...getInputProps()} className="sr-only" />

        <div className="flex flex-col items-center gap-4">
          <div>
            <UploadIllustration />
          </div>
          <div className="space-y-2">
            <p className="font-medium text-sm">
              Drop files here or{' '}
              <button
                className="cursor-pointer text-primary underline-offset-4 hover:underline"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent parent div click handler
                  openFileDialog();
                }}
                type="button"
              >
                browse files
              </button>
            </p>
            <p className="text-muted-foreground text-xs">
              Maximum file size: {formatBytes(maxSize)} • Maximum files:{' '}
              {maxFiles}
            </p>
          </div>
        </div>
      </div>

      {/* Files Table */}
      {uploadFiles.length > 0 && (
        <div className="min-h-[calc(100vh-10rem)] w-full space-y-4 bg-background p-6">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">
              Files ({uploadFiles.length})
            </h3>
            <div className="flex gap-2">
              <Button onClick={openFileDialog} size="sm" variant="outline">
                <CloudUpload />
                Add files
              </Button>
              <Button onClick={clearFiles} size="sm" variant="outline">
                <Trash2 />
                Remove all
              </Button>
              <Button disabled={isUploading} onClick={uploadAllFiles} size="sm">
                {isUploading ? (
                  <Spinner text="Uploading..." />
                ) : (
                  <div className="flex items-center gap-1">
                    <CloudUpload />
                    Upload all
                  </div>
                )}
              </Button>
            </div>
          </div>

          <div className="rounded-lg border">
            <Table>
              <TableHeader className="bg-muted">
                <TableRow className="text-xs">
                  <TableHead className="h-9">Name</TableHead>
                  <TableHead className="h-9">Type</TableHead>
                  <TableHead className="h-9">Size</TableHead>
                  <TableHead className="h-9 w-[100px] text-end">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {uploadFiles.map((fileItem) => (
                  <TableRow key={fileItem.id}>
                    <TableCell className="py-2 ps-1.5">
                      <div className="flex items-center gap-1">
                        <div
                          className={cn(
                            'relative flex size-8 shrink-0 items-center justify-center text-muted-foreground/80'
                          )}
                        >
                          {fileItem.status === 'uploading' ? (
                            <div className="relative">
                              {/* Circular progress background */}
                              <svg
                                className="-rotate-90 size-8"
                                viewBox="0 0 32 32"
                              >
                                <title>Loading...</title>
                                <circle
                                  className="text-muted-foreground/20"
                                  cx="16"
                                  cy="16"
                                  fill="none"
                                  r="14"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                />
                                {/* Progress circle */}
                                <circle
                                  className="text-primary transition-all duration-300"
                                  cx="16"
                                  cy="16"
                                  fill="none"
                                  r="14"
                                  stroke="currentColor"
                                  strokeDasharray={`${2 * Math.PI * 14}`}
                                  strokeDashoffset={`${2 * Math.PI * 14 * (1 - fileItem.progress / 100)}`}
                                  strokeLinecap="round"
                                  strokeWidth="2"
                                />
                              </svg>
                              {/* File icon in center */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                {getFileIcon(fileItem.file)}
                              </div>
                            </div>
                          ) : (
                            <div className="flex not-[]:size-8 items-center justify-center">
                              {getFileIcon(fileItem.file)}
                            </div>
                          )}
                        </div>
                        <HoverCard>
                          <HoverCardTrigger asChild>
                            <p className="flex cursor-pointer items-center gap-1 truncate font-medium text-sm">
                              {fileItem.file.name}
                              {fileItem.status === 'error' && (
                                <Badge variant="destructive">Error</Badge>
                              )}
                            </p>
                          </HoverCardTrigger>
                          <HoverCardContent className="p-2">
                            <AspectRatio
                              className="rounded-lg bg-muted"
                              ratio={16 / 9}
                            >
                              {fileItem.preview ? (
                                <Image
                                  alt="Photo by Drew Beamer"
                                  className="h-full w-full rounded-lg object-cover dark:brightness-[0.2] dark:grayscale"
                                  fill
                                  src={fileItem.preview}
                                />
                              ) : (
                                <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                                  <p className="text-sm">
                                    No preview available
                                  </p>
                                </div>
                              )}
                            </AspectRatio>
                          </HoverCardContent>
                        </HoverCard>
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <Badge className="text-xs" variant="secondary">
                        {getFileTypeLabel(fileItem.file)}
                      </Badge>
                    </TableCell>
                    <TableCell className="py-2 text-muted-foreground text-sm">
                      {formatBytes(fileItem.file.size)}
                    </TableCell>
                    <TableCell className="py-2 pe-1">
                      <div className="flex items-center gap-1">
                        {fileItem.preview && (
                          <Button
                            className="size-8 text-muted-foreground"
                            disabled={isUploading}
                            onClick={() =>
                              handleFileUpload(
                                fileItem.file as File,
                                fileItem.id
                              )
                            }
                            size="icon"
                            title="Download"
                            variant={'ghost'}
                          >
                            {fileItem.status === 'uploading' || isUploading ? (
                              <LoaderIcon className="size-4 animate-spin " />
                            ) : (
                              <CloudUpload className="size-4" />
                            )}
                          </Button>
                        )}
                        {fileItem.status === 'error' ? (
                          <Button
                            className="size-8 text-destructive/80 hover:text-destructive"
                            onClick={() => retryUpload(fileItem.id)}
                            size="icon"
                          >
                            <RefreshCwIcon className="size-3.5" />
                          </Button>
                        ) : (
                          <Button
                            className="size-8 text-destructive"
                            disabled={isUploading}
                            onClick={() => removeUploadFile(fileItem.id)}
                            size="icon"
                            variant={'ghost'}
                          >
                            <Trash2 className="size-3.5" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <Alert appearance="light" className="mt-5" variant="destructive">
          <AlertIcon>
            <TriangleAlert />
          </AlertIcon>
          <AlertContent>
            <AlertTitle>File upload error(s)</AlertTitle>
            <AlertDescription>
              {errors.map((error, index) => (
                <p className="last:mb-0" key={index}>
                  {error}
                </p>
              ))}
            </AlertDescription>
          </AlertContent>
        </Alert>
      )}
    </div>
  );
}
