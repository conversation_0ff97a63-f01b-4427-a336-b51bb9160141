// convex/helpers/canUpdateArticle.ts
import { Id } from "../_generated/dataModel";
import { QueryCtx } from "../_generated/server";
import { requireUser } from "../users";

export async function canUpdateArticle(
  ctx: QueryCtx,
  articleId: Id<"articles">
) {
  const userId = await requireUser(ctx);
  const article = await ctx.db.get(articleId);
  if (!article) {
    return { allowed: false, error: "Article not found." };
  }

  const user = await ctx.db.get(userId);
  if (!user) {
    return { allowed: false, error: "User not found." };
  }

  const isOwner = article.userId === userId;
  const isAdmin = user.role === "admin";

  if (!isOwner && !isAdmin) {
    return { allowed: false, error: "Unauthorized." };
  }

  return { allowed: true, article };
}

export async function getArticleOrFail(ctx: QueryCtx, id: Id<"articles">) {
  const article = await ctx.db.get(id);
  if (!article) return { success: false, error: "Article not found." };
  return { success: true, article };
}

export async function generateSlug(
  ctx: QueryCtx,
  title: string,
  excludeId?: Id<"articles">
) {
  let slug =
    title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "") || `untitled-${Date.now()}`;

  const existing = await ctx.db
    .query("articles")
    .withIndex("by_slug", (q) => q.eq("slug", slug))
    .unique();

  if (existing && existing._id !== excludeId) {
    slug += `-${Date.now()}`;
  }

  return slug;
}
