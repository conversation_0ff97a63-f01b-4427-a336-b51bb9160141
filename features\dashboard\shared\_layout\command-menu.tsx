'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import type { DialogProps } from '@radix-ui/react-dialog';
import {
  Laptop,
  LayoutListIcon,
  LogOutIcon,
  Moon,
  MoveRightIcon,
  PlusIcon,
  SearchIcon,
  SettingsIcon,
  Sun,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import React from 'react';
import { toast } from 'sonner';
import { CommandMenuKbd } from '@/components/custom/command-kbd';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  adminNavItems,
  authorNavItems,
  mediaNavItems,
} from '@/config/dashboard';
export type UserRole = 'admin' | 'author' | 'media-manager' | 'viewer';

export function CommandMenuTop({
  basePath,
  role,
  username,
  ...props
}: DialogProps & {
  basePath: string;
  role: UserRole;
  username: string;
}) {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState('');
  const { setTheme } = useTheme();
  const { signOut } = useAuthActions();

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || e.key === '/') {
        if (
          (e.target instanceof HTMLElement && e.target.isContentEditable) ||
          e.target instanceof HTMLInputElement ||
          e.target instanceof HTMLTextAreaElement ||
          e.target instanceof HTMLSelectElement
        ) {
          return;
        }

        e.preventDefault();
        setOpen((op) => !op);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const runCommand = React.useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);
  let navItems = authorNavItems;
  if (role === 'admin') {
    navItems = adminNavItems;
  }
  if (role === 'media-manager') {
    navItems = mediaNavItems;
  }
  const handleSignOut = () => {
    try {
      signOut();
      toast.success('You have been logged out.');
    } catch {
      toast.error('Failed to log out.');
    }
  };

  return (
    <>
      <div>
        <Button
          className="size-8 rounded-full bg-transparent lg:hidden"
          onClick={() => setOpen(true)}
          size={'icon'}
          variant="outline"
          {...props}
        >
          <SearchIcon className="size-5" />
          <span className="sr-only">Open find</span>
        </Button>
        <Button
          className="relative hidden h-8 w-48 cursor-pointer justify-start rounded-sm text-muted-foreground text-sm shadow-none hover:text-muted-foreground lg:flex"
          onClick={() => setOpen(true)}
          variant="outline"
          {...props}
        >
          <SearchIcon className="size-4" />
          <span className="hidden text-sm sm:inline">Find...</span>

          <div className="ml-auto flex items-center gap-1">
            <CommandMenuKbd className="aspect-square bg-transparent p-1">
              Ctrl
            </CommandMenuKbd>
            <CommandMenuKbd className="aspect-square bg-transparent p-1">
              K
            </CommandMenuKbd>
          </div>
        </Button>
      </div>

      <CommandDialog
        className="rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'What do you need?'}
        />

        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>
          </CommandEmpty>
          <CommandGroup className="pt-2" heading="Profile">
            <CommandItem
              key="settings"
              onSelect={() => {
                runCommand(() => router.push(`${basePath}/settings`));
              }}
              value="settings"
            >
              <SettingsIcon />
              Settings
            </CommandItem>
            <CommandItem key="logout" onSelect={handleSignOut} value="logout">
              <LogOutIcon />
              Logout
            </CommandItem>
          </CommandGroup>
          {role === 'author' ||
            (role === 'admin' && (
              <CommandGroup heading="Articles">
                <CommandItem
                  key="Search Article"
                  onSelect={() => {
                    runCommand(() => {
                      const event = new KeyboardEvent('keydown', {
                        key: 'a',
                        ctrlKey: true,
                        bubbles: true,
                      });
                      document.dispatchEvent(event);
                    });
                  }}
                  value="search article"
                >
                  <LayoutListIcon />
                  Search Article..
                </CommandItem>
                <CommandItem
                  key="Create Article"
                  onSelect={() => {
                    runCommand(() => router.push(`${basePath}/editor`));
                  }}
                  value="create article"
                >
                  <PlusIcon />
                  Create New Article..
                </CommandItem>
              </CommandGroup>
            ))}

          <CommandGroup heading="Navigation">
            {navItems.map((item) => (
              <CommandItem
                key={item.label}
                onSelect={() => {
                  runCommand(() => router.push(item.link));
                }}
                value={item.label}
              >
                <MoveRightIcon />
                Go to{' '}
                <span className="font-medium">{username}'s dashboard</span>{' '}
                {item.label}
              </CommandItem>
            ))}
          </CommandGroup>

          <CommandGroup heading="Theme">
            <CommandItem onSelect={() => runCommand(() => setTheme('light'))}>
              <Sun />
              Light
            </CommandItem>
            <CommandItem onSelect={() => runCommand(() => setTheme('dark'))}>
              <Moon />
              Dark
            </CommandItem>
            <CommandItem onSelect={() => runCommand(() => setTheme('system'))}>
              <Laptop />
              System
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
