import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { CircleXIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { rejectionMessageSchema, type TRejectionMessageSchema } from './schema';

export default function AddRejectionMessageToMedia({
  mediaId,
}: {
  mediaId: Id<'mediaFiles'>;
}) {
  const [open, setOpen] = useState(false);
  const rejectMedia = useMutation(api.admin.rejectMediaFile);
  const form = useForm<TRejectionMessageSchema>({
    resolver: zodResolver(rejectionMessageSchema),
    defaultValues: {
      message: '',
    },
  });

  const onSubmit = async (data: TRejectionMessageSchema) => {
    try {
      await rejectMedia({ id: mediaId, message: data.message });
      toast.success('Media rejected successfully!');
      setOpen(false);
    } catch {
      toast.error('Failed to reject media.');
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      <DialogTrigger asChild>
        <Button
          className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
          onClick={() => setOpen(true)}
          variant="ghost"
        >
          <span>Reject</span>
          <CircleXIcon className="size-4 text-muted-foreground" />
        </Button>
      </DialogTrigger>
      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>Reject Media</DialogTitle>
          <DialogDescription>
            Please enter a rejection message.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="flex flex-col gap-4"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea
                      className="resize-none"
                      placeholder="Type your message..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit">Reject</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
