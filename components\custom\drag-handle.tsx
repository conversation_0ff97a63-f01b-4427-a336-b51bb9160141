'use client';

import { GripVerticalIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function DragHandle() {
  return (
    <Button
      className="size-7 cursor-grab text-muted-foreground hover:bg-transparent"
      size="icon"
      variant="ghost"
    >
      <GripVerticalIcon className="size-3 text-muted-foreground" />
      <span className="sr-only">Drag to reorder</span>
    </Button>
  );
}
