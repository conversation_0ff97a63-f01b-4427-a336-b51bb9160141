// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { Skeleton } from '@/components/ui/skeleton';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function AdminArticleAnalyticsCards() {
  const totalStagedArticles = useQuery(api.admin.getTotalArticlesByStatus, {
    status: 'staged',
  });
  const totalApprovedArticles = useQuery(api.admin.getTotalArticlesByStatus, {
    status: 'approved',
  });
  const totalPublishedArticles = useQuery(api.admin.getTotalArticlesByStatus, {
    status: 'published',
  });
  const totalDeletedArticles = useQuery(api.admin.getTotalArticlesByStatus, {
    status: 'deleted',
  });

  const isLoading =
    totalStagedArticles === undefined ||
    totalApprovedArticles === undefined ||
    totalPublishedArticles === undefined ||
    totalDeletedArticles === undefined;

  if (isLoading) {
    return (
      <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4 py-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton
            className="min-h-[150px] rounded-sm p-6 shadow-xs"
            key={index}
          />
        ))}
      </div>
    );
  }
  const cards: TPageCard[] = [
    {
      title: 'Staged Articles',
      value: totalStagedArticles,
      badgeText: 'All',
      icon: getIconByStatus('staged'),
      description: 'Visible to administrators',
      footer: 'Awaiting admin review',
    },
    {
      title: 'Approved Articles',
      value: totalApprovedArticles,
      badgeText: 'All',
      icon: getIconByStatus('approved'),
      description: 'Approved articles by admin',
      footer: 'Visible to the staff',
    },
    {
      title: 'Published Articles',
      value: totalPublishedArticles,
      badgeText: 'All',
      icon: getIconByStatus('published'),
      description: 'Published articles',
      footer: 'Visible to the public',
    },
    {
      title: 'Deleted Articles',
      value: totalDeletedArticles,
      badgeText: 'All',
      icon: getIconByStatus('deleted'),
      description: 'Marked for removal by author',
      footer: 'Deleted articles',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
