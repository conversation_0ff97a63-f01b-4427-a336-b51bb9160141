import type { Metadata } from 'next';
import '@/styles/globals.css';

import { ConvexAuthNextjsServerProvider } from '@convex-dev/auth/nextjs/server';
import { GeistMono } from 'geist/font/mono';
import { GeistSans } from 'geist/font/sans';
import Providers from '@/components/providers';
import { siteConfig } from '@/config/web';

export const metadata: Metadata = {
  title: {
    absolute: 'Better Flow | The workflow that turns drafts into articles',
    template: `%s - ${siteConfig.name}`,
  },
  description: siteConfig.description,
  creator: siteConfig.name,
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: '@rathonagency',
  },
  metadataBase: new URL(siteConfig.url),
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ConvexAuthNextjsServerProvider>
      <html
        className={`${GeistSans.variable} ${GeistMono.variable}`}
        lang="en"
        suppressHydrationWarning
      >
        <body>
          <Providers>{children}</Providers>
        </body>
      </html>
    </ConvexAuthNextjsServerProvider>
  );
}
