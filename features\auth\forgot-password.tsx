'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { forgotPasswordSchema, type TforgotPasswordSchema } from './schema';

export default function ForgotForm() {
  const { signIn } = useAuthActions();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [flow] = useState<'reset' | 'signUp'>('reset');

  const form = useForm<TforgotPasswordSchema>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const handleSubmit = async (values: TforgotPasswordSchema) => {
    try {
      setIsLoading(true);
      //   Replace with your forgot password logic
      await signIn('password', {
        flow,
        email: values.email,
        redirectTo: `/reset-password?email=${values.email}`,
      });
      form.reset();
      toast.success('Reset link sent successfully to your email!');
      router.push(`/reset-password?email=${values.email}`);
    } catch {
      toast('An error occurred while sending the reset password link.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="flex flex-col gap-8">
      <header className="flex flex-col gap-2">
        <h1 className="font-bold text-2xl">Forgot Password</h1>
        <p className="text-muted-foreground text-sm">
          Enter your email address to receive a password reset link.
        </p>
      </header>

      <Form {...form}>
        <form className="grid gap-6" onSubmit={form.handleSubmit(handleSubmit)}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    className="rounded-none"
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            className="w-full rounded-none"
            disabled={isLoading}
            type="submit"
          >
            {isLoading ? <Spinner text="Sending..." /> : 'Send Reset Link'}
          </Button>
        </form>
      </Form>

      <p className="mt-6 text-center text-sm">
        Back to{' '}
        <Link className="underline underline-offset-4" href="/sign-in">
          Sign in
        </Link>
      </p>
    </section>
  );
}
