'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { GoTrash } from 'react-icons/go';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PhoneInput } from '@/components/ui/phone-input';
import { api } from '@/convex/_generated/api';
import { phoneFormSchema, type TPhoneFormValues } from '../../schema';
export function PhoneCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const updatePhone = useMutation(api.users.updatePhone);
  const removePhone = useMutation(api.users.removePhone);
  const form = useForm<TPhoneFormValues>({
    resolver: zodResolver(phoneFormSchema),
    defaultValues: { phone: user?.phone || '' },
  });

  useEffect(() => {
    if (user?.phone) {
      form.reset({ phone: user.phone });
    }
  }, [user?.phone, form]);

  if (!user) {
    return null;
  }
  const handleSubmit = (values: TPhoneFormValues) => {
    try {
      updatePhone({ phone: values.phone });
      toast.success(values.phone);
      toast.success('Phone number updated successfully!');
    } catch {
      toast.error('Failed to update phone number.');
    }
  };
  const handleRemovePhone = () => {
    try {
      removePhone({});
      toast.success('Phone number removed successfully!');
    } catch {
      toast.error('Failed to remove phone number.');
    }
  };
  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40 "
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">
              Your Phone Number
            </h2>
            <p className="font-normal text-primary/60 text-sm">
              Enter a phone number to receive important service updates by SMS.
            </p>
          </div>
          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">Phone Number</FormLabel>
                  <FormControl>
                    <PhoneInput
                      className="w-fit rounded-e-sm lg:max-w-xs dark:bg-black"
                      placeholder="Phone number"
                      {...field}
                      defaultCountry="RW"
                      international
                      max={15}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              className="text-destructive hover:text-destructive"
              onClick={handleRemovePhone}
              size={'icon'}
              variant={'ghost'}
            >
              <GoTrash className="size-4" />
            </Button>
          </div>
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            A code will be sent to verify your phone number.
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
