import { LayoutGridIcon, TableIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';

type View = 'grid' | 'table';

type ViewToggleProps = {
  view: View;
  setView: (view: View) => void;
};

export default function ViewToggle({ view, setView }: ViewToggleProps) {
  const handleViewChange = (newView: View) => {
    setView(newView);
    localStorage.setItem('articleView', newView);
  };

  return (
    <div className="flex items-center justify-center rounded-lg bg-background p-1">
      <Button
        aria-label="Grid View"
        className={cn(
          'rounded-lg text-muted-foreground',
          view !== 'grid' && 'hover:bg-transparent hover:text-primary'
        )}
        onClick={() => handleViewChange('grid')}
        size="icon"
        variant={view === 'grid' ? 'secondary' : 'ghost'}
      >
        <LayoutGridIcon className="size-4" />
      </Button>
      <Button
        aria-label="Table View"
        className={cn(
          'rounded-lg text-muted-foreground',
          view !== 'table' && 'hover:bg-transparent hover:text-primary'
        )}
        onClick={() => handleViewChange('table')}
        size="icon"
        variant={view === 'table' ? 'secondary' : 'ghost'}
      >
        <TableIcon className="size-4" />
      </Button>
    </div>
  );
}
