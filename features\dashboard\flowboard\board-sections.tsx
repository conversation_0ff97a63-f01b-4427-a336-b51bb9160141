// 'use client';

// import { useQuery } from 'convex/react';
// import { api } from '@/convex/_generated/api';
// import BoardColumns from './board-columns';
// import CreateOrUpdateColumn from './create-edit-column';

// export default function BoardSections() {
//   const allBoards = useQuery(api.board.getAllBoards) || [];
//   if (!(allBoards && Array.isArray(allBoards))) {
//     return null;
//   }

//   return (
//     <div className="px-4 py-10">
//       {allBoards.map((board) => (
//         <div className="flex flex-col gap-6" key={board._id}>
//           <div className="flex items-center justify-between" key={board._id}>
//             <h2 className="font-medium text-3xl">{board.name}</h2>
//             <CreateOrUpdateColumn boardId={board._id} />
//           </div>
//           <BoardColumns boardId={board._id} />
//         </div>
//       ))}
//     </div>
//   );
// }
