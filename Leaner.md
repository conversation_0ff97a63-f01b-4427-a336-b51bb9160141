# This is file for showing all used command and error faced

## To see only your directory not whole path you type this:

function prompt { "PS $(Split-Path -Leaf -Path $PWD)> " }

## to run both backend and frontend

pnpm add -D npm-run-all

pnpm add @convex-dev/auth @auth/core@0.37.0
pnpm dlx @convex-dev/auth

# to go to convex dashboard

pnpm dlx convex dashboard

# TO GET CONVEX SITE URL

pnpm dlx convex env get CONVEX_SITE_URL
