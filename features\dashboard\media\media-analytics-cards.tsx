'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function MediaAnalyticsCards() {
  const totalDraftMedia = useQuery(api.media.getTotalMediaFilesByStatus, {
    status: 'draft',
  });
  const totalStagedMedia = useQuery(api.media.getTotalMediaFilesByStatus, {
    status: 'staged',
  });
  const totalApprovedMedia = useQuery(api.media.getTotalMediaFilesByStatus, {
    status: 'approved',
  });

  const totalDeletedMedia = useQuery(api.media.getTotalMediaFilesByStatus, {
    status: 'deleted',
  });

  const isLoading =
    totalDraftMedia === undefined ||
    totalStagedMedia === undefined ||
    totalApprovedMedia === undefined ||
    totalDeletedMedia === undefined;

  if (isLoading) {
    return <div>Loading analytics...</div>;
  }
  const cards: TPageCard[] = [
    {
      title: 'Draft',
      value: totalDraftMedia,
      badgeText: 'All',
      icon: getIconByStatus('draft'),
      description: 'Only visible to the author',
      footer: 'Author drafts',
    },
    {
      title: 'Staged',
      value: totalStagedMedia,
      badgeText: 'All',
      icon: getIconByStatus('staged'),
      description: 'Visible to administrators',
      footer: 'Awaiting admin review',
    },
    {
      title: 'Approved',
      value: totalApprovedMedia,
      badgeText: 'All',
      icon: getIconByStatus('approved'),
      description: 'Approved media by admin',
      footer: 'Visible to the staff',
    },
    {
      title: 'Deleted',
      value: totalDeletedMedia,
      badgeText: 'All',
      icon: getIconByStatus('deleted'),
      description: 'Marked for removal by author',
      footer: 'Deleted media',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
