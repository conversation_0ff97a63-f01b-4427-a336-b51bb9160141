import { type ReactMutation, useMutation, useQuery } from 'convex/react';
import type { FunctionReference } from 'convex/server';
import {
  CircleCheckBigIcon,
  EllipsisIcon,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddGroupToMedia from './add-group';
import CreateOrUpdateMedia from './create-edit-media';
import AddRejectionMessageToMedia from './rejection-message';
import type { TMedia } from './types';

export function MediaCardActions({ media }: { media: TMedia }) {
  const user = useQuery(api.users.getUser);

  // Media Mutations
  const deleteMedia = useMutation(api.media.deleteMediaFileTemp);
  const restoreMedia = useMutation(api.media.restoreMediaFile);
  const toggleFavoriteMedia = useMutation(api.media.toggleFavoriteMediaFile);
  const stageMedia = useMutation(api.media.stageMediaFile);
  const unStageMedia = useMutation(api.media.unStageMediaFile);

  // Admin mutations
  const approveMedia = useMutation(api.admin.approveMediaFile);
  const publishMedia = useMutation(api.admin.publishMediaFile);
  const unapproveMedia = useMutation(api.admin.unapproveMediaFile);

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            id: Id<'mediaFiles'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { id: Id<'mediaFiles'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        if (mutation === publishMedia) {
          if (!('success' in media || media.groupId)) {
            toast.error('Media must be in a group to be published.');
            return;
          }
          if (!('success' in media || media.url)) {
            toast.error('Media must have a url to be published.');
            return;
          }
        }

        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    [media, publishMedia]
  );
  if (!media || 'success' in media) {
    return null;
  }
  if (!user || 'success' in user) {
    return null;
  }
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button size={'icon'} variant="ghost">
            <EllipsisIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="-right-11 absolute w-56 px-2 py-4">
          <div className="flex flex-col">
            {/* Favorite toggle */}
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  toggleFavoriteMedia,
                  { id: media._id },
                  media.isFavorite
                    ? ' Media removed from favorite!'
                    : 'Media added to favorite!',
                  'Failed to toggle favorite media.'
                )
              }
              variant="ghost"
            >
              <span>
                {media.isFavorite ? 'Remove from favorite' : 'Add to favorite'}
              </span>
              <StarIcon
                className={`size-4 ${
                  media.isFavorite
                    ? 'fill-primary text-muted-foreground'
                    : 'text-muted-foreground'
                }`}
              />
            </Button>

            {/* Edit */}
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() => setOpenEditDialog(true)}
              variant={'ghost'}
            >
              <span>Edit</span>
              <PenToolIcon className="size-4 text-muted-foreground" />
            </Button>

            {/* Delete / Restore */}
            {media.status === 'deleted' ? (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                onClick={() =>
                  handleAction(
                    restoreMedia,
                    { id: media._id },
                    'Media restored successfully!',
                    'Failed to restore media.'
                  )
                }
                variant="ghost"
              >
                <span>Restore</span>
                <UndoIcon className="size-4 text-muted-foreground" />
              </Button>
            ) : (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                disabled={
                  media.status === 'published' || media.status === 'approved'
                }
                onClick={() =>
                  handleAction(
                    deleteMedia,
                    { id: media._id },
                    'Media deleted successfully!',
                    'Failed to delete media.'
                  )
                }
                variant="ghost"
              >
                <span>Delete</span>
                <TrashIcon className="size-4 text-muted-foreground" />
              </Button>
            )}

            {/* Author actions */}
            {user.role === 'media-manager' && (
              <div>
                {media.status === 'staged' ? (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unStageMedia,
                        { id: media._id },
                        'Media unstaged successfully!',
                        'Failed to unstage media.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Unstage</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </Button>
                ) : (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    disabled={
                      media.status === 'published' ||
                      media.status === 'approved'
                    }
                    onClick={() =>
                      handleAction(
                        stageMedia,
                        { id: media._id },
                        'Media staged successfully!',
                        'Failed to stage media.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Stage</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </Button>
                )}
              </div>
            )}

            {/* Admin actions */}
            {user.role === 'admin' && (
              <>
                {media.status === 'staged' && (
                  <>
                    <AddRejectionMessageToMedia mediaId={media._id} />
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          approveMedia,
                          { id: media._id },
                          'Media approved successfully!',
                          'Failed to approve media.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Approve</span>
                      <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                    </Button>
                  </>
                )}

                {media.status === 'approved' && (
                  <>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          unapproveMedia,
                          { id: media._id },
                          'Media unapproved successfully!',
                          'Failed to unapprove media.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Unapprove</span>
                      <UndoIcon className="size-4 text-muted-foreground" />
                    </Button>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          publishMedia,
                          { id: media._id },
                          'Media published successfully!',
                          'Failed to publish media.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Publish</span>
                      <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                    </Button>
                  </>
                )}

                {media.status === 'published' && (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unapproveMedia,
                        { id: media._id },
                        'Media unpublished successfully!',
                        'Failed to unpublish media.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Unpublish</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </Button>
                )}
                <AddGroupToMedia
                  mediaId={media._id}
                  prevGroupId={media.groupId as Id<'groups'>}
                  status={media.status}
                />
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>
      <CreateOrUpdateMedia
        id={media._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={media.title}
        url={media.url}
      />
    </>
  );
}
