'use client';
import { type Preloaded, usePreloadedQuery } from 'convex/react';
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { useTheme } from 'next-themes';
import Player from 'next-video/player';
import { AudioPlayer } from '@/components/custom/audio-player';
import { PageContainer } from '@/components/custom/page-container';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import type { api } from '@/convex/_generated/api';
import { formatBytes } from '@/hooks/use-file-upload';
import noCoverDark from '@/public/noCover_black.jpg';
import noCoverWhite from '@/public/noCover_white.jpg';
import { getIconByMediaType } from '../shared/icons';

type TMedia = Preloaded<typeof api.media.getMediaFileBySlug>;
export default function MediaDetails({
  preloadedMedia,
}: {
  preloadedMedia: TMedia;
}) {
  const file = usePreloadedQuery(preloadedMedia);

  if (!file || 'success' in file) {
    return null;
  }
  const MediaIcon = getIconByMediaType(file.contentType);
  return (
    <PageContainer>
      <div className="@container/main container flex min-h-screen flex-col gap-8 bg-background py-8 lg:gap-10">
        <div className="@container grid w-full grid-cols-12 py-4">
          <div className="@lg:col-span-8 @md:col-span-10 col-span-12 @lg:col-start-3 @md:col-start-2">
            <article className="relative flex flex-col items-center gap-5 text-center">
              {/* Meta Info */}
              <div className="flex flex-wrap items-center justify-center gap-4 text-sm lg:gap-10">
                {/* Title */}
                <div className="flex items-center gap-2">
                  <MediaIcon className="size-6 text-muted-foreground" />
                  <h1 className="scroll-m-20 font-semibold text-2xl tracking-tight">
                    {file.title}
                  </h1>
                </div>

                <span className="text-primary">
                  {formatDistanceToNow(new Date(file._creationTime), {
                    addSuffix: true,
                  })}
                </span>

                <p className="text-muted-foreground">
                  {formatBytes(file.size || 0)}
                </p>
                <p className="text-muted-foreground">{file.status}</p>
              </div>

              {/* Description */}
              {file.description && file.description.length > 0 ? (
                <p className="line-clamp-3 max-w-4xl text-muted-foreground">
                  {file.description}
                </p>
              ) : (
                <p className="line-clamp-3 max-w-4xl text-muted-foreground">
                  No description
                </p>
              )}
            </article>
          </div>
        </div>
        {file.contentType === 'image' && (
          <ImageCoverImage title={file.title} url={file.url} />
        )}
        {file.contentType === 'video' && (
          <VideoPlayer title={file.title} url={file.url} />
        )}
        {file.contentType === 'audio' && (
          <AudioPlayerSection title={file.title} url={file.url} />
        )}
        {file.contentType === 'application' && (
          <div className="relative @lg:col-span-8 @md:col-span-10 col-span-12 @lg:col-start-3 @md:col-start-2">
            <p className="text-muted-foreground text-sm">
              No preview available for this file type.
            </p>
          </div>
        )}
      </div>
    </PageContainer>
  );
}

function ImageCoverImage({ url, title }: { url: string; title: string }) {
  return (
    <div className="relative @lg:col-span-8 @md:col-span-10 col-span-12 @lg:col-start-3 @md:col-start-2">
      <div className="relative mx-auto max-h-[470px] max-w-4xl overflow-hidden rounded-lg">
        <AspectRatio ratio={834 / 470}>
          <Image
            alt={title}
            className="rounded-lg object-cover"
            fill
            priority
            sizes="(max-width: 834px) 100vw, 834px"
            src={url}
          />
        </AspectRatio>
      </div>
    </div>
  );
}

function VideoPlayer({ title, url }: { title: string; url: string }) {
  return (
    <div className="relative @lg:col-span-8 @md:col-span-10 col-span-12 @lg:col-start-3 @md:col-start-2">
      <div className="ma-h-[570px] relative mx-auto max-w-4xl overflow-hidden rounded-lg">
        <AspectRatio className="w-full" ratio={16 / 9}>
          <Player
            autoPlay={false}
            className="aspect-video w-full bg-background"
            // poster={noCover.src}
            controls
            loop
            muted
            src={url}
            title={title}
          />
        </AspectRatio>
      </div>
    </div>
  );
}

function AudioPlayerSection({ title, url }: { title: string; url: string }) {
  const { resolvedTheme } = useTheme();
  const noCover = resolvedTheme === 'dark' ? noCoverDark : noCoverWhite;
  return (
    <div className="relative @lg:col-span-8 @md:col-span-10 col-span-12 @lg:col-start-3 @md:col-start-2">
      <div className="relative mx-auto max-w-4xl overflow-hidden md:hidden">
        <AudioPlayer
          height={32}
          mimeType="audio/mp3"
          src={url}
          thumbnail={noCover.src}
          title={title}
        />
      </div>
      <div className="relative mx-auto hidden max-w-4xl overflow-hidden md:block">
        <AudioPlayer
          mimeType="audio/mp3"
          src={url}
          thumbnail={noCover.src}
          title={title}
        />
      </div>
    </div>
  );
}
