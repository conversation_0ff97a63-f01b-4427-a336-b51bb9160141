'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/convex/_generated/api';
import { bioFormSchema, type TBioFormValues } from '../schema';
export function BioCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const updateBio = useMutation(api.users.updateBio);
  const form = useForm<TBioFormValues>({
    resolver: zodResolver(bioFormSchema),
    defaultValues: { bio: user?.bio || '' },
  });
  useEffect(() => {
    if (user?.bio) {
      form.reset({ bio: user.bio });
    }
  }, [user?.bio, form]);
  if (!user) {
    return null;
  }
  const handleSubmit = (values: TBioFormValues) => {
    try {
      updateBio({ bio: values.bio });
      toast.success('Bio updated successfully!');
    } catch {
      toast.error('Failed to update bio.');
    }
  };
  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">Bio</h2>
            <p className="font-normal text-primary/60 text-sm">
              Please enter your bio. This will be displayed on your profile.
            </p>
          </div>
          <FormField
            control={form.control}
            name="bio"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Bio</FormLabel>
                <FormControl>
                  <Textarea
                    className="resize-none"
                    placeholder="Bio"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            Please use 160 characters at maximum.
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
