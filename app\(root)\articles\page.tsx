import { PageContainer } from '@/components/custom/page-container';
import ArticleCard from '@/features/root/article-card';
import { getArticles } from '@/features/root/data';
import type { TArticle } from '@/features/root/types';

export default async function ArticlesPage() {
  const articles = await getArticles();
  if (!articles) return null;
  // console.log('articles:', articles);
  return (
    <PageContainer>
      <div className="grid grid-cols-1 gap-x-3 gap-y-8 py-10 md:grid-cols-2 md:gap-y-16 lg:grid-cols-3 ">
        {articles.map((article: TArticle) => (
          <ArticleCard article={article} key={article.title} />
        ))}
      </div>
    </PageContainer>
  );
}
