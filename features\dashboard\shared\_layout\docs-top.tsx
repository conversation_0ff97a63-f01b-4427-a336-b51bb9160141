import { BookOpenIcon, SquareArrowOutUpRightIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

const docsLink = [
  {
    label: 'ChangeLog',
    href: '#',
    external: true,
  },
  {
    label: 'Help',
    href: '#',
    external: true,
  },
  {
    label: 'Docs',
    href: '#',
    external: true,
  },
];

export function PopoverDocsTop() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="hidden size-8 rounded-full bg-transparent text-muted-foreground hover:text-primary lg:flex"
          size={'icon'}
          variant="outline"
        >
          <BookOpenIcon className="size-5" />
          <span className="sr-only">Open docs</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="absolute right-4 mt-4 w-48 p-2">
        <div className="flex flex-col">
          {docsLink.map((link, i) => (
            <Button
              className="w-full justify-start"
              key={link.label}
              variant={i === 0 ? 'secondary' : 'ghost'}
            >
              <a
                className="flex w-full items-center justify-between"
                href={link.href}
                target={link.external ? '_blank' : '_self'}
              >
                <div>{link.label}</div>
                <div>
                  {link.external && (
                    <SquareArrowOutUpRightIcon className="size-5 text-muted-foreground" />
                  )}
                </div>
              </a>
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
