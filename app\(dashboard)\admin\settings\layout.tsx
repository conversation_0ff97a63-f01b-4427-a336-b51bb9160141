import { PageContainer } from '@/components/custom/page-container';
import { Separator } from '@/components/ui/separator';
import SettingsHeader from '@/features/dashboard/settings/components/header';
import { SettingLinksNavbar } from '@/features/dashboard/settings/components/navbar-links';

const LayoutContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <SettingsHeader />
      <Separator />
      <PageContainer className="relative mx-auto flex h-full w-full max-w-screen-xl gap-2.5 py-10">
        <SettingLinksNavbar />
        <main className="flex flex-1">{children}</main>
      </PageContainer>
    </>
  );
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return <LayoutContainer>{children}</LayoutContainer>;
}
