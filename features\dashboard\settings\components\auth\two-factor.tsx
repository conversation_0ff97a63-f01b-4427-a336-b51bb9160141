'use client';
import { type Preloaded, usePreloadedQuery } from 'convex/react';
import type { api } from '@/convex/_generated/api';

export default function TwoFactorAuth({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  if (!user) {
    return null;
  }
  return (
    <div className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40 ">
      <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
        <div className="flex flex-col gap-2">
          <h2 className="font-medium text-primary text-xl">
            Two-factor Authentication
          </h2>
          <p className="font-normal text-primary/60 text-sm">
            Add an additional layer of security by requiring at least two
            methods of authentication to sign in.
          </p>
        </div>
      </div>
      <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-5 md:flex-row md:items-center dark:bg-card">
        <p className="font-normal text-primary/60 text-sm">
          Emails must be verified to be able to login with them or be used as
          primary email.
        </p>
      </div>
    </div>
  );
}
