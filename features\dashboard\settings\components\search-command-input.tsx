'use client';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { buttonVariants } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { settingLinks } from '@/config/dashboard';
import { cn } from '@/lib/utils';

export function SearchCommandInput() {
  const pathname = usePathname();
  return (
    <Command className="rounded-lg border shadow-md md:min-w-[450px]">
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        {/* <CommandGroup heading="Suggestions">
          <CommandItem>
            <Calendar />
            <span>Calendar</span>
          </CommandItem>
          <CommandItem>
            <Smile />
            <span>Search Emoji</span>
          </CommandItem>
          <CommandItem disabled>
            <Calculator />
            <span>Calculator</span>
          </CommandItem>
        </CommandGroup> */}

        <div className="flex flex-col">
          {settingLinks.map((item) => (
            <CommandItem key={item.label}>
              <Link
                className={cn(
                  `${buttonVariants({ variant: 'ghost', size: 'lg' })}`,
                  'justify-start rounded-md px-4'
                )}
                href={item.href}
                key={item.label}
              >
                <span
                  className={cn(
                    'font-normal text-muted-foreground text-sm',
                    pathname === item.href && 'font-medium text-primary'
                  )}
                >
                  {item.label}
                </span>
              </Link>
            </CommandItem>
          ))}
        </div>
      </CommandList>
    </Command>
  );
}
