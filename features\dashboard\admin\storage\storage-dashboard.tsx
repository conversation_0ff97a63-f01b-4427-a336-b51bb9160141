import { PageContainer } from '@/components/custom/page-container';
import { StorageChartInMB } from './chart-in-mb';
import { StorageChartInNumber } from './chart-in-num';

export default function StorageDashboard() {
  return (
    <PageContainer className="relative flex h-full w-full gap-2.5 py-10">
      <div className="flex h-full min-h-[calc(100vh-200px)] w-full flex-col gap-4 rounded-lg bg-background p-4 py-6 lg:flex-row">
        <StorageChartInNumber />
        <StorageChartInMB />
      </div>
    </PageContainer>
  );
}
