'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { Skeleton } from '@/components/ui/skeleton';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function AdminStaffAnalyticsCards() {
  const totalUsers = useQuery(api.admin.getTotalUsers);
  const totalVerifiedUsers = useQuery(api.admin.getTotalVerifiedUsers);
  const totalAuthors = useQuery(api.admin.getTotalUsersByRole, {
    role: 'author',
  });
  const totalMediaManagers = useQuery(api.admin.getTotalUsersByRole, {
    role: 'media-manager',
  });
  const totalAdsManagers = useQuery(api.admin.getTotalUsersByRole, {
    role: 'ads-manager',
  });
  const isLoading =
    totalUsers === undefined ||
    totalVerifiedUsers === undefined ||
    totalAuthors === undefined ||
    totalMediaManagers === undefined ||
    totalAdsManagers === undefined;

  if (isLoading) {
    return (
      <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4 py-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton
            className="min-h-[150px] rounded-sm p-6 shadow-xs"
            key={index}
          />
        ))}
      </div>
    );
  }

  const cards: TPageCard[] = [
    // {
    //   title: 'Total Users',
    //   value: totalUsers,
    //   badgeText: 'All',
    //   icon: getIconByStatus('total'),
    //   description: 'Total users',
    //   footer: 'Total users',
    // },
    {
      title: 'Verified Users',
      value: totalVerifiedUsers,
      badgeText: 'All',
      icon: getIconByStatus('verified'),
      description: 'Verified users',
      footer: 'Verified users',
    },
    {
      title: 'Authors',
      value: totalAuthors,
      badgeText: 'All',
      icon: getIconByStatus('author'),
      description: 'Authors',
      footer: 'Authors',
    },
    {
      title: 'Media Managers',
      value: totalMediaManagers,
      badgeText: 'All',
      icon: getIconByStatus('media-manager'),
      description: 'Media Managers',
      footer: 'Media Managers',
    },
    {
      title: 'Ads Managers',
      value: totalAdsManagers,
      badgeText: 'All',
      icon: getIconByStatus('ads-manager'),
      description: 'Ads Managers',
      footer: 'Ads Managers',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
