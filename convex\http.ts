import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { getArticles } from "./api";
import { auth } from "./auth";
import { resend } from "./emails";

const http = httpRouter();

auth.addHttpRoutes(http);
http.route({
  path: "/resend-webhook",
  method: "POST",
  handler: httpAction(async (ctx, req) => {
    return await resend.handleResendEventWebhook(ctx, req);
  }),
});

http.route({
  path: "/articles",
  method: "GET",
  handler: getArticles,
});

export default http;
