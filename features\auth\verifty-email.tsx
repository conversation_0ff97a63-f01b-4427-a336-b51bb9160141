'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { api } from '@/convex/_generated/api';
import { type TverifyEmailSchema, verifyEmailSchema } from './schema';

export default function VerifyEmailForm() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const { signIn } = useAuthActions();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [flow] = useState<'email-verification' | 'signUp'>(
    'email-verification'
  );
  const verifyUserEmail = useMutation(api.users.addUserEmail);

  const form = useForm<TverifyEmailSchema>({
    resolver: zodResolver(verifyEmailSchema),
    defaultValues: {
      pin: '',
    },
  });

  const handleSubmit = async (values: TverifyEmailSchema) => {
    try {
      setIsLoading(true);
      if (!email) {
        toast.error('Email is required.');
        return;
      }

      await signIn('password', {
        flow,
        code: values.pin,
        email,
        redirectTo: '/sign-in',
      });
      await verifyUserEmail({ email, isVerified: true });
      form.reset();
      router.push('/sign-in');
      toast('Your email has been verified.');
    } catch {
      toast('An error occurred while verifying your email.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="flex flex-col gap-8">
      <header className="flex flex-col gap-2">
        <h1 className="font-bold text-2xl">Verify Email</h1>
        <p className="text-muted-foreground text-sm">
          Enter the code sent to your email to verify your account.
        </p>
      </header>

      <Form {...form}>
        <form className="grid gap-6" onSubmit={form.handleSubmit(handleSubmit)}>
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <InputOTP maxLength={8} {...field}>
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                      <InputOTPSlot index={6} />
                      <InputOTPSlot index={7} />
                    </InputOTPGroup>
                  </InputOTP>
                </FormControl>
                <FormDescription>
                  Please enter the code sent to your email.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            className="w-full rounded-none"
            disabled={isLoading}
            type="submit"
          >
            {isLoading ? <Spinner text="Verifying..." /> : 'Verify Email'}
          </Button>
        </form>
      </Form>
    </section>
  );
}
