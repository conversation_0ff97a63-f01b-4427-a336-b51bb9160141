'use client';

import { LoaderCircleIcon, SearchIcon } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

import { Input } from '@/components/ui/input';

export default function SearchInput({
  searchText,
  setSearchText,
}: {
  searchText: string;
  setSearchText: (text: string) => void;
}) {
  const id = useId();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!searchText) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
      // here you could trigger a search API call
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  return (
    <div className="mb-10">
      {' '}
      <div className="relative min-h-10 bg-background">
        <Input
          aria-label="Search"
          className="peer min-h-10 ps-9"
          id={id}
          onChange={(e) => setSearchText(e.target.value)}
          placeholder="Search..."
          type="search"
          value={searchText}
        />
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
          {isLoading ? (
            <LoaderCircleIcon
              aria-label="Loading results"
              className="animate-spin"
              role="status"
              size={16}
            />
          ) : (
            <SearchIcon aria-hidden="true" size={16} />
          )}
        </div>
      </div>
    </div>
  );
}
