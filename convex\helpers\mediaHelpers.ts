import { ROLES } from "../utils/constants";
import { QueryCtx } from "../_generated/server";
import { requireUser } from "../users";
import { Id } from "../_generated/dataModel";

export async function canAccessMediaManager(ctx: QueryCtx) {
  const userId = await requireUser(ctx);
  const user = await ctx.db.get(userId);
  if (!user) return { allowed: false, error: "User not found." };
  if (user.role !== ROLES.MEDIA_MANAGER && user.role !== ROLES.ADMIN) {
    return { allowed: false, error: "Unauthorized." };
  }
  return { allowed: true, user };
}

export async function generateSlug(
  ctx: QueryCtx,
  title: string,
  excludeId?: Id<"mediaFiles">
) {
  let slug =
    title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "") || `untitled-${Date.now()}`;

  const existing = await ctx.db
    .query("mediaFiles")
    .withIndex("by_slug", (q) => q.eq("slug", slug))
    .unique();

  if (existing && existing._id !== excludeId) {
    slug += `-${Date.now()}`;
  }

  return slug;
}
