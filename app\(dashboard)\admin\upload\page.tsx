import type { Metada<PERSON> } from 'next';
import { PageContainer } from '@/components/custom/page-container';
import TableUpload from '@/components/table-upload';
export const metadata: Metadata = {
  title: 'Upload',
  description: 'Upload',
};

export default function UploadPage() {
  return (
    <PageContainer className="flex flex-col gap-6 py-6">
      <TableUpload />
    </PageContainer>
  );
}
