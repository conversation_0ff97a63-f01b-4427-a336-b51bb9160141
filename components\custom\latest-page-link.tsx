'use client';

import { ArrowBigRightDashIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '../ui/button';

export default function BackButton({ className }: { className?: string }) {
  const router = useRouter();

  return (
    <Button
      className={className}
      onClick={() => router.back()}
      variant={'ghost'}
    >
      <div className="flex items-center gap-1 font-medium">
        <span>Return Home</span>
        <ArrowBigRightDashIcon className="size-4" />
      </div>
    </Button>
  );
}
