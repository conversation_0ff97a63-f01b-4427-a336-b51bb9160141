import { useAuthActions } from '@convex-dev/auth/react';
import { type Preloaded, usePreloadedQuery } from 'convex/react';
import Link from 'next/link';
import { toast } from 'sonner';
import { CommandMenuKbd } from '@/components/custom/command-kbd';
import { ThemeSwitcher } from '@/components/mode/theme-switcher';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  userDropdownGroup1,
  userDropdownGroup2,
  userDropdownGroup3,
} from '@/config/dashboard';
import type { api } from '@/convex/_generated/api';
import profile from '@/public/profile.svg';

export function UserDropdown({
  preloadedUser,
  basePath,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
  basePath: string;
}) {
  const { signOut } = useAuthActions();
  const user = usePreloadedQuery(preloadedUser);

  if (!user) {
    return null;
  }
  const handleSignOut = () => {
    try {
      signOut();
      toast.success('You have been logged out.');
    } catch {
      toast.error('Failed to log out.');
    }
  };

  return (
    <div className="hidden items-center gap-4 lg:flex">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Avatar className="size-8 cursor-pointer rounded-full">
            <AvatarImage alt={'user'} src={user.avatarUrl || profile.src} />
            <AvatarFallback className="rounded-full">
              {user.name?.charAt(0)}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="mt-4 w-(--radix-dropdown-menu-trigger-width) min-w-64 rounded-sm bg-background"
          sideOffset={4}
        >
          {/* <UserDropdownContent /> */}
          <DropdownMenuLabel className="px-2 font-normal ">
            <div className="flex flex-col gap-1 px-1 py-1.5 text-left text-sm">
              <span className="font-medium capitalize">{user.name}</span>
              <span className="text-muted-foreground ">{user.email}</span>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup className="p-2">
            {userDropdownGroup1.map((item) => {
              const fullHref = `${basePath}${item.href}`;
              return (
                <DropdownMenuItem
                  asChild
                  className="cursor-pointer lg:min-h-10"
                  key={item.label}
                >
                  <Link
                    className="flex items-center justify-between gap-2"
                    href={fullHref}
                  >
                    {item.label}
                    <item.icon className="size-4" />
                  </Link>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup className="p-2">
            {userDropdownGroup2.map((item) => {
              let component: React.ReactNode = null;
              if (item.custom === 'command') {
                component = (
                  <div className="flex items-center gap-1">
                    <CommandMenuKbd className="aspect-square bg-transparent p-1">
                      Ctrl
                    </CommandMenuKbd>
                    <CommandMenuKbd className="aspect-square bg-transparent p-1">
                      K
                    </CommandMenuKbd>
                  </div>
                );
              }
              if (item.custom === 'theme') {
                component = <ThemeSwitcher />;
              }

              return (
                <DropdownMenuItem
                  asChild
                  className="cursor-pointer lg:min-h-10"
                  key={item.label}
                  onClick={(e) => {
                    if (item.custom === 'theme') {
                      e.preventDefault();
                    }
                  }}
                >
                  <div className="flex items-center justify-between gap-2">
                    {item.label}
                    {component}
                  </div>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup className="p-2">
            {userDropdownGroup3.map((item) => (
              <DropdownMenuItem
                asChild
                className="cursor-pointer lg:min-h-10"
                key={item.label}
                onClick={() => {
                  if (item.label === 'Logout') {
                    handleSignOut();
                  }
                }}
              >
                <Link
                  className="flex items-center justify-between gap-2"
                  href={item.href}
                >
                  {item.label}
                  <item.icon className="size-4" />
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// export function UserDropdownContent() {
//   return (
//     <>
//       <DropdownMenuLabel className="px-2 font-normal ">
//         <div className="flex flex-col gap-1 px-1 py-1.5 text-left text-sm">
//           <span className="font-medium ">Lecon</span>
//           <span className="text-muted-foreground "><EMAIL></span>
//         </div>
//       </DropdownMenuLabel>
//       <DropdownMenuSeparator />
//       <DropdownMenuGroup className="p-2">
//         {userDropdownGroup1.map((item) => (
//           <DropdownMenuItem
//             asChild
//             className="cursor-pointer lg:min-h-10"
//             key={item.label}
//           >
//             <Link className="flex items-center justify-between gap-2" href="">
//               {item.label}
//               <item.icon className="size-4" />
//             </Link>
//           </DropdownMenuItem>
//         ))}
//       </DropdownMenuGroup>
//       <DropdownMenuSeparator />
//       <DropdownMenuGroup className="p-2">
//         {userDropdownGroup2.map((item) => {
//           let component: React.ReactNode = null;
//           if (item.custom === 'command') {
//             component = (
//               <div className="flex items-center gap-1">
//                 <CommandMenuKbd className="aspect-square bg-transparent p-1">
//                   Ctrl
//                 </CommandMenuKbd>
//                 <CommandMenuKbd className="aspect-square bg-transparent p-1">
//                   K
//                 </CommandMenuKbd>
//               </div>
//             );
//           }
//           if (item.custom === 'theme') {
//             component = <ModeSwitcher />;
//           }

//           return (
//             <DropdownMenuItem
//               asChild
//               className="cursor-pointer lg:min-h-10"
//               key={item.label}
//             >
//               <Link className="flex items-center justify-between gap-2" href="">
//                 {item.label}
//                 {component}
//               </Link>
//             </DropdownMenuItem>
//           );
//         })}
//       </DropdownMenuGroup>
//       <DropdownMenuSeparator />
//       <DropdownMenuGroup className="p-2">
//         {userDropdownGroup3.map((item) => (
//           <DropdownMenuItem
//             asChild
//             className="cursor-pointer lg:min-h-10"
//             key={item.label}
//           >
//             <Link className="flex items-center justify-between gap-2" href="">
//               {item.label}
//               <item.icon className="size-4" />
//             </Link>
//           </DropdownMenuItem>
//         ))}
//       </DropdownMenuGroup>
//     </>
//   );
// }
