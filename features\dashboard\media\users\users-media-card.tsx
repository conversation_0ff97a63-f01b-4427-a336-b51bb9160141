'use client';
import {
  CopyIcon,
  DownloadIcon,
  ShareIcon,
  ShieldAlertIcon,
} from 'lucide-react';
import Image from 'next/image';
import Player from 'next-video/player';
import { toast } from 'sonner';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import type { TMedia } from '@/features/dashboard/media/types';
export default function UsersMediaCard({ media }: { media: TMedia }) {
  if (!media || 'success' in media) {
    return null;
  }
  const handleCopyUrl = () => {
    try {
      navigator.clipboard.writeText(media.url);
      // wait 1 sec to show toast
      setTimeout(() => {
        toast.success('URL copied to clipboard!');
      }, 1000);
    } catch {
      toast.error('Failed to copy URL');
    }
  };
  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = media.url;
      link.download = media.title;
      link.click();
      toast.success('Download started!');
    } catch {
      toast.error('Failed to download');
    }
  };
  return (
    <ContextMenu key={media._id}>
      <ContextMenuTrigger asChild className="relative">
        <button
          className="relative w-full cursor-pointer overflow-hidden rounded-lg"
          onClick={handleCopyUrl}
          type="button"
        >
          <AspectRatio
            className="rounded-lg bg-[#fafafa] dark:bg-black"
            ratio={16 / 9}
          >
            {media.url ? (
              <>
                {media.contentType === 'image' && (
                  <Image
                    alt="Photo by Drew Beamer"
                    className="h-full w-full rounded-lg object-cover "
                    fill
                    src={media.url}
                  />
                )}
                {media.contentType === 'video' && (
                  <Player
                    autoPlay={false}
                    className="aspect-video w-full bg-[#fafafa] object-contain dark:bg-black"
                    controls
                    loop
                    muted
                    src={media.url}
                    title={media.title}
                  />
                )}
                {media.contentType === 'audio' && (
                  <audio
                    className="h-full w-full rounded-lg object-cover"
                    controls
                    controlsList="nodownload"
                    playsInline
                    preload="metadata"
                    src={media.url}
                    title={media.title}
                  >
                    <track kind="captions" />
                  </audio>
                )}
                {media.contentType === 'application' && (
                  <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                    <p className="text-sm">No preview available</p>
                  </div>
                )}
              </>
            ) : (
              <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                <p className="text-sm">No preview available</p>
              </div>
            )}
          </AspectRatio>
        </button>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={handleCopyUrl}
        >
          Copy Url
          <CopyIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={handleDownload}
        >
          Download
          <DownloadIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem className="flex cursor-pointer items-center justify-between">
          Share
          <ShareIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem className="flex cursor-pointer items-center justify-between">
          Report
          <ShieldAlertIcon className="size-4" />
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
