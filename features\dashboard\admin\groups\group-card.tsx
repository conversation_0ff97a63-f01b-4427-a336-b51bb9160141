import { useQuery } from 'convex/react';
import type { FunctionReturnType } from 'convex/server';
import { formatDistanceToNow } from 'date-fns';
import {
  CheckLineIcon,
  FileBoxIcon,
  FileImageIcon,
  FilePenLineIcon,
} from 'lucide-react';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import GroupCardActions from './group-card.actions';

export type TGroup = FunctionReturnType<typeof api.groups.getGroup>;

export default function GroupCard({ group }: { group: TGroup }) {
  const articleCount =
    useQuery(
      api.groups.countArticlesInGroup,
      group && !('success' in group)
        ? { groupId: group._id as Id<'groups'> }
        : 'skip'
    ) || 0;

  const mediaCount =
    useQuery(
      api.groups.countMediaInGroup,
      group && !('success' in group) ? { groupId: group._id } : 'skip'
    ) || 0;

  if (!group || 'success' in group) {
    return null;
  }

  return (
    <Card className="w-full rounded-sm shadow-xs ring-ring/20 hover:ring dark:bg-background dark:ring-ring/40">
      <CardHeader>
        <CardTitle className="mr-7 flex items-center gap-2">
          <FileBoxIcon className="size-4 shrink-0 text-muted-foreground" />
          <span className="truncate capitalize">{group.name}</span>
        </CardTitle>
        <CardDescription className="mr-10 truncate">
          {group.description
            ? group.description.length > 50
              ? `${group.description.slice(0, 50)}...`
              : group.description
            : 'No description'}
        </CardDescription>
        <CardAction>
          <GroupCardActions group={group} />
        </CardAction>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <div className="flex items-center gap-1">
          <CheckLineIcon className="size-4 text-muted-foreground" />
          <p className="font-medium text-sm capitalize tracking-tight">
            {group.status}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <FileImageIcon className="size-4 text-muted-foreground" />
            <p className="text-muted-foreground text-sm">
              {typeof mediaCount === 'number' ? mediaCount : 0} Media
            </p>
          </div>
          <div className="flex items-center gap-1">
            <FilePenLineIcon className="size-4 text-muted-foreground" />
            <p className="text-muted-foreground text-sm capitalize tracking-tight">
              {typeof articleCount === 'number' ? articleCount : 0} Articles
            </p>
          </div>
          <p className="text-muted-foreground text-sm">
            {formatDistanceToNow(new Date(group._creationTime), {
              addSuffix: true,
            })}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
