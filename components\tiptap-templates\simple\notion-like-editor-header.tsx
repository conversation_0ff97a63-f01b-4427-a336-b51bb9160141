'use client';

// --- Styles ---
import { CharCountButton } from '@/components/tiptap-ui/char-count-button/char-count-button';
import { UndoRedoButton } from '@/components/tiptap-ui/undo-redo-button';
// --- Tiptap UI ---
import { ButtonGroup } from '@/components/tiptap-ui-primitive/button';
// --- UI Primitives ---
import './notion-like-editor-header.scss';

export function NotionEditorHeader() {
  return (
    <header className="sticky top-10 z-10 hidden justify-end lg:flex">
      <div className="flex flex-col items-center justify-end gap-3 px-6 py-2">
        <CharCountButton />
        <ButtonGroup
          className="ml-auto flex items-center justify-end"
          orientation="horizontal"
        >
          <UndoRedoButton action="undo" />
          <UndoRedoButton action="redo" />
        </ButtonGroup>
      </div>
    </header>
  );
}
