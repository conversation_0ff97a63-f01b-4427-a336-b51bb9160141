import type { FunctionReturnType } from 'convex/server';
import { formatDistanceToNow } from 'date-fns';
import {
  BriefcaseBusinessIcon,
  FileUserIcon,
  UserCheckIcon,
  UserXIcon,
} from 'lucide-react';
import Link from 'next/link';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import type { api } from '@/convex/_generated/api';
import { cn } from '@/lib/utils';
import StaffCardAction from './staff-card-action';

export type TStaff = FunctionReturnType<typeof api.admin.getStaffMember>;

export default function StaffItemCard({ staff }: { staff: TStaff }) {
  if (!staff || 'success' in staff) {
    return null;
  }

  return (
    <Card
      className={cn(
        'w-full rounded-sm shadow-xs ring-ring/20 hover:ring dark:bg-background dark:ring-ring/40',
        !staff.verified && 'ring-1 ring-[#f49ca2ca] dark:ring-[#551a1e]'
      )}
      key={staff._id}
    >
      <CardHeader>
        <CardTitle className="mr-7 flex items-center gap-2">
          <FileUserIcon className="size-4 shrink-0 text-muted-foreground" />
          <span className="truncate capitalize">{staff.name}</span>
        </CardTitle>
        <CardDescription className="mr-10 truncate">
          <Link className="lowercase hover:underline" href="/">
            {staff.username}.betterflow.com
          </Link>
        </CardDescription>
        <CardAction>
          <StaffCardAction staff={staff} />
        </CardAction>
      </CardHeader>
      <Link href="/author/editor">
        <CardContent className="flex flex-col gap-2">
          <div className="flex items-center gap-1">
            {staff.verified ? (
              <UserCheckIcon className="size-4 text-muted-foreground" />
            ) : (
              <UserXIcon className="size-4 text-muted-foreground" />
            )}
            <p className="font-medium text-sm capitalize tracking-tight">
              {staff.verified ? 'Verified' : 'Not Verified'}
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <BriefcaseBusinessIcon className="size-4 text-muted-foreground" />
              <p className="text-muted-foreground text-sm">{staff.role}</p>
            </div>
            <p className="text-muted-foreground text-sm">
              <span>Joined</span>{' '}
              <span>
                {formatDistanceToNow(new Date(staff._creationTime), {
                  addSuffix: false,
                })}
              </span>
            </p>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}
