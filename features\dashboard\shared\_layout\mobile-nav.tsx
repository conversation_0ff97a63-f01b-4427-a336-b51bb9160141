'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import Link from 'next/link';
import React from 'react';
import { toast } from 'sonner';
import { CommandMenuKbd } from '@/components/custom/command-kbd';
import { ModeSwitcher } from '@/components/mode/mode-switcher';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  userDropdownGroup1,
  userDropdownGroup2,
  userDropdownGroup3,
} from '@/config/dashboard';
import { cn } from '@/lib/utils';

export function MobileNav({
  className,
  basePath,
}: {
  className?: string;
  basePath: string;
}) {
  const { signOut } = useAuthActions();
  const [open, setOpen] = React.useState(false);
  const handleSignOut = () => {
    try {
      signOut();
      toast.success('You have been logged out.');
    } catch {
      toast.error('Failed to log out.');
    }
  };

  return (
    <Popover onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            'size-8 rounded-full bg-transparent lg:hidden',
            className
          )}
          size={'icon'}
          variant="outline"
        >
          <div className="relative flex h-8 w-4 items-center justify-center">
            <div className="relative size-4">
              <span
                className={cn(
                  'absolute left-0 block h-[1px] w-4 bg-foreground transition-all duration-100',
                  open ? '-rotate-45 top-[0.4rem]' : 'top-1'
                )}
              />
              <span
                className={cn(
                  'absolute left-0 block h-[1px] w-4 bg-foreground transition-all duration-100',
                  open ? 'top-[0.4rem] rotate-45' : 'top-2.5'
                )}
              />
            </div>
            <span className="sr-only">Toggle Menu</span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        alignOffset={-16}
        className="no-scrollbar h-(--radix-popper-available-height) w-(--radix-popper-available-width) overflow-y-auto rounded-none border-none bg-background p-0 shadow-none backdrop-blur duration-100"
        side="bottom"
        sideOffset={8}
      >
        <div className="flex flex-col gap-12 overflow-auto py-6">
          <div className="flex flex-col gap-4">
            <div className="px-6 font-medium text-muted-foreground text-sm">
              Menu
            </div>
            <div className="flex flex-col gap-3">
              <div className="px-5 py-1.5 font-medium text-sm data-[inset]:pl-8'">
                <div className="flex flex-col gap-1 px-1 py-1.5 text-left text-sm">
                  <span className="font-medium ">Lecon</span>
                  <span className="text-muted-foreground ">
                    <EMAIL>
                  </span>
                </div>
              </div>
              <div className="h-px bg-border" />
              <div className="p-2">
                {userDropdownGroup1.map((item) => {
                  const fullHref = `${basePath}${item.href}`;
                  return (
                    <Button
                      asChild
                      className="group flex min-h-12 w-full cursor-pointer justify-between px-4 py-1.5"
                      key={item.label}
                      variant={'ghost'}
                    >
                      <Link
                        className="flex w-full items-center justify-between "
                        href={fullHref}
                      >
                        <span className="font-normal text-[16px] text-muted-foreground group-hover:text-primary ">
                          {item.label}
                        </span>
                        <item.icon className="size-4 text-muted-foreground group-hover:text-primary" />
                      </Link>
                    </Button>
                  );
                })}
              </div>
              <div className="h-px bg-border" />
              <div className="p-2">
                {userDropdownGroup2.map((item) => {
                  let component: React.ReactNode = null;
                  if (item.custom === 'command') {
                    component = (
                      <div className="flex items-center gap-1">
                        <CommandMenuKbd className="aspect-square bg-transparent p-1">
                          Ctrl
                        </CommandMenuKbd>
                        <CommandMenuKbd className="aspect-square bg-transparent p-1">
                          K
                        </CommandMenuKbd>
                      </div>
                    );
                  }
                  if (item.custom === 'theme') {
                    component = <ModeSwitcher />;
                  }
                  return (
                    <Button
                      asChild
                      className={cn(
                        'group min-h-12 w-full justify-between px-4 py-1.5',
                        item.custom === 'theme' && 'cursor-default'
                      )}
                      key={item.label}
                      variant={'ghost'}
                    >
                      <Link
                        className="flex w-full items-center justify-between gap-2 text-muted-foreground group-hover:text-primary"
                        href=""
                      >
                        <span className="font-normal text-[16px] ">
                          {item.label}
                        </span>
                        {component}
                      </Link>
                    </Button>
                  );
                })}
              </div>
              <div className="h-px bg-border" />
              <div className="p-2">
                {userDropdownGroup3.map((item) => (
                  <Button
                    asChild
                    className="group flex min-h-12 w-full cursor-pointer justify-between px-4 py-1.5"
                    key={item.label}
                    onClick={() => {
                      if (item.label === 'Logout') {
                        handleSignOut();
                      }
                    }}
                    variant={'ghost'}
                  >
                    <Link
                      className="flex w-full items-center justify-between gap-2 text-muted-foreground group-hover:text-primary"
                      href={item.href}
                    >
                      <span className="font-normal text-[16px] ">
                        {item.label}
                      </span>
                      <item.icon className="size-4 text-muted-foreground group-hover:text-primary" />
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
