'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { UploadIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { api } from '@/convex/_generated/api';
import { SearchStatus } from '@/features/dashboard/shared/search/search-status';
import SearchTop from '@/features/dashboard/shared/search/search-top';
import ViewToggle from '../shared/components/view-toggle';
import MediaCard from './media-card';
import MediaTable from './media-table';
import SearchMediaMobile from './search-media-mobile';

export default function MediaCards({ isAdmin }: { isAdmin?: boolean }) {
  const managerMedia = useQuery(api.media.listMediaFiles) || [];
  const adminMedia = useQuery(api.admin.getMediaFilesByAdmin) || [];
  const allMedia = isAdmin
    ? adminMedia && 'data' in adminMedia
      ? adminMedia.data
      : []
    : managerMedia;
  const [searchText, setSearchText] = useState('');
  const [view, setView] = useState<'grid' | 'table'>('grid');
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'draft',
    'staged',
    'approved',
    'published',
    'deleted',
  ]);
  const searchResults =
    useQuery(api.media.searchMediaFile, {
      query: searchText,
    }) || [];
  useEffect(() => {
    const storedView = localStorage.getItem('articleView');
    if (storedView === 'grid' || storedView === 'table') {
      setView(storedView);
    } else {
      localStorage.setItem('articleView', 'grid');
    }
  }, []);
  const filteredMedia = useMemo(() => {
    if (!(allMedia && Array.isArray(allMedia))) {
      return [];
    }

    return allMedia.filter((media) => selectedStatuses.includes(media.status));
  }, [allMedia, selectedStatuses]);
  const handleStatusChange = (statuses: string[]) => {
    setSelectedStatuses(statuses);
  };
  const mediaFiles = searchText ? searchResults : filteredMedia;
  if (!(mediaFiles && Array.isArray(mediaFiles))) {
    return null;
  }
  return (
    <>
      <div className="flex items-center justify-between gap-2">
        <SearchTop
          searchPlaceholder="Search Media..."
          searchText={searchText}
          setSearchText={setSearchText}
        />
        <SearchMediaMobile basePath={basePath} medias={mediaFiles} />
        <SearchStatus
          onStatusChange={handleStatusChange}
          selectedStatuses={selectedStatuses}
        />
        <ViewToggle setView={setView} view={view} />
        <Button variant={'default'}>
          <Link className="flex items-center gap-1" href={`${basePath}/upload`}>
            <UploadIcon className="hidden size-4 md:block" />
            Upload
          </Link>
        </Button>
      </div>
      {view === 'grid' ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {mediaFiles.map((media) => (
            <MediaCard basePath={basePath} key={media._id} media={media} />
          ))}
          {mediaFiles.length === 0 && (
            <div className="col-span-full flex flex-col items-center justify-center gap-2">
              <p className="text-muted-foreground text-sm">No media found.</p>
            </div>
          )}
        </div>
      ) : (
        <MediaTable medias={mediaFiles} />
      )}
    </>
  );
}
