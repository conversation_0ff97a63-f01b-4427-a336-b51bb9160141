'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import { Skeleton } from '@/components/ui/skeleton';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function AdminMediaAnalyticsCards() {
  const totalStagedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'staged',
  });
  const totalApprovedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'approved',
  });
  const totalPublishedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'published',
  });
  const totalDeletedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'deleted',
  });
  const isLoading =
    totalStagedMedia === undefined ||
    totalApprovedMedia === undefined ||
    totalPublishedMedia === undefined ||
    totalDeletedMedia === undefined;

  if (isLoading) {
    return (
      <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4 py-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton
            className="min-h-[150px] rounded-sm p-6 shadow-xs"
            key={index}
          />
        ))}
      </div>
    );
  }

  const cards: TPageCard[] = [
    {
      title: 'Staged Media',
      value: totalStagedMedia,
      badgeText: 'All',
      icon: getIconByStatus('staged'),
      description: 'Visible to administrators',
      footer: 'Awaiting admin review',
    },
    {
      title: 'Approved Media',
      value: totalApprovedMedia,
      badgeText: 'All',
      icon: getIconByStatus('approved'),
      description: 'Approved media by admin',
      footer: 'Visible to the staff',
    },
    {
      title: 'Published Media',
      value: totalPublishedMedia,
      badgeText: 'All',
      icon: getIconByStatus('published'),
      description: 'Published media',
      footer: 'Visible to the public',
    },
    {
      title: 'Deleted Media',
      value: totalDeletedMedia,
      badgeText: 'All',
      icon: getIconByStatus('deleted'),
      description: 'Marked for removal by author',
      footer: 'Deleted media',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
