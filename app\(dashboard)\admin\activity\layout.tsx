import { PageContainer } from '@/components/custom/page-container';
import { Separator } from '@/components/ui/separator';
import { ActivityLinksNavbar } from '@/features/dashboard/activity/components/activity-navbar';
import ActivityHeader from '@/features/dashboard/activity/components/header';

const LayoutContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <ActivityHeader />
      <Separator />
      <PageContainer className="relative flex h-full w-full gap-2.5 py-10 ">
        <ActivityLinksNavbar />
        {children}
      </PageContainer>
    </>
  );
};
export default function Layout({ children }: { children: React.ReactNode }) {
  return <LayoutContainer>{children}</LayoutContainer>;
}
