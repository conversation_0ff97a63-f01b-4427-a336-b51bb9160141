'use client';
import type { FunctionReturnType } from 'convex/server';
import { DataTable } from '@/components/custom/data-table';
import type { api } from '@/convex/_generated/api';
import { articlesColumns } from './columns';

export type TAllArticles = FunctionReturnType<typeof api.articles.getArticles>;
export type TArticle = FunctionReturnType<typeof api.articles.getArticle>;

export default function ArticleTable({ articles }: { articles: TAllArticles }) {
  if (!(articles && Array.isArray(articles))) {
    return null;
  }
  // Handle empty articles state
  if (articles.length === 0) {
    return (
      <div className="p-4 text-muted-foreground">
        No articles found. Create your first article!
      </div>
    );
  }

  return (
    <div className=" rounded-md bg-background p-4">
      <DataTable columns={articlesColumns} data={articles} />
    </div>
  );
}
