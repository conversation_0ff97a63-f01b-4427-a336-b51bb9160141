import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery, preloadQuery } from 'convex/nextjs';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { PageContainer } from '@/components/custom/page-container';
import { api } from '@/convex/_generated/api';
import EditorForm from '@/features/dashboard/editor/editor-form';

type Props = {
  params: Promise<{ slug: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const article = await fetchQuery(
    api.articles.getArticle,
    { slug },
    { token: await convexAuthNextjsToken() }
  );
  if (!article || 'success' in article) {
    return notFound();
  }
  return {
    title: `Article - ${article.title}`,
    description: article.description,
  };
}

export default async function EditorPage({ params }: Props) {
  const { slug } = await params;
  const article = await preloadQuery(
    api.articles.getArticle,
    { slug },
    { token: await convexAuthNextjsToken() }
  );
  if (!article || 'success' in article) {
    return notFound();
  }

  return (
    <PageContainer>
      <EditorForm preloadedArticle={article} />
    </PageContainer>
  );
}
