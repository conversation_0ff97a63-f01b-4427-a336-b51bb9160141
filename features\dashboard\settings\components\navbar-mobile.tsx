'use client';
import { MenuIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { settingLinks } from '@/config/dashboard';
import { cn } from '@/lib/utils';

export function NavbarMobile() {
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;
  return (
    <Drawer>
      <DrawerTrigger asChild>
        <Button
          className="bg-background lg:hidden"
          size={'icon'}
          variant={'outline'}
        >
          <MenuIcon className="size-5" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="sr-only">
          <DrawerTitle>Settings</DrawerTitle>
          <DrawerDescription>Settings navigation links.</DrawerDescription>
        </DrawerHeader>
        <div className="flex flex-col gap-5 p-6">
          {settingLinks.map((item) => {
            const fullHref = `${basePath}${item.href}`;
            return (
              <div key={item.label}>
                {item.comingSoon ? (
                  <span className="text-muted-foreground">
                    {item.label} (Coming Soon)
                  </span>
                ) : (
                  <Link href={fullHref}>
                    <span
                      className={cn(
                        'font-normal text-muted-foreground text-sm',
                        pathname.endsWith(item.href) &&
                          'font-medium text-primary'
                      )}
                    >
                      {item.label}
                    </span>
                  </Link>
                )}
              </div>
            );
          })}
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button>Close</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
