'use client';

import { ArmchairIcon, ArrowUpIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import NavItem from '@/components/custom/nav-item';
import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import {
  adminNavItems,
  authorNavItems,
  mediaNavItems,
} from '@/config/dashboard';
import { cn } from '@/lib/utils';
export type UserRole = 'admin' | 'author' | 'media-manager' | 'viewer';

type HeaderNavsProps = {
  role: UserRole;
};

export default function HeaderNavs({ role }: HeaderNavsProps) {
  const pathname = usePathname();

  const [showLogo, setShowLogo] = useState(false);
  const [showArrowUp, setShowArrowUp] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowLogo(window.scrollY > 60);
      setShowArrowUp(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  let navItems = authorNavItems;
  if (role === 'admin') {
    navItems = adminNavItems;
  }
  if (role === 'media-manager') {
    navItems = mediaNavItems;
  }

  return (
    <nav
      className={cn(
        'w-full gap-4 border-border border-b bg-transparent',
        showLogo &&
          'bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90'
      )}
    >
      <div className="flex items-center gap-4 px-4 transition-all duration-1000 ease-in sm:px-6">
        {showLogo && (
          <Link href="/">
            <ArmchairIcon className="size-6 text-primary" />
          </Link>
        )}
        <ScrollArea className="w-[100vw] overflow-hidden lg:w-full">
          <div className="mx-auto flex w-full items-center ">
            {navItems.map((item, index) => (
              <NavItem
                active={pathname === item.link}
                id={index}
                item={item}
                key={item.label}
              />
            ))}
          </div>
          <ScrollBar className="h-0 w-0" orientation="horizontal" />
        </ScrollArea>

        {showArrowUp && (
          <Button
            className="ml-auto hidden rounded-full lg:flex"
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            size={'icon'}
            variant={'ghost'}
          >
            <ArrowUpIcon className="size-6 text-muted-foreground" />
          </Button>
        )}
      </div>
    </nav>
  );
}
