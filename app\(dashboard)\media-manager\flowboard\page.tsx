import type { Metadata } from 'next';
import { PageContainer } from '@/components/custom/page-container';
import { Separator } from '@/components/ui/separator';
import KanbanFlowboard from '@/features/dashboard/flowboard/board';
import FlowboardHeader from '@/features/dashboard/flowboard/header';
export const metadata: Metadata = {
  title: 'Flowboard',
  description: 'Flowboard for media manager',
};

export default function FlowboardPage() {
  return (
    <div>
      <FlowboardHeader />
      <Separator />
      <PageContainer className="py-10">
        <KanbanFlowboard />
      </PageContainer>
    </div>
  );
}
