'use client';
import { useMutation, useQuery } from 'convex/react';
import { format } from 'date-fns';
import { BriefcaseBusinessIcon, MessageCirclePlusIcon } from 'lucide-react';
import { useCallback, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import profileImg from '@/public/profile.svg';
export default function StartConversation({
  className,
  iconOnly,
}: {
  className?: string;
  iconOnly?: boolean;
}) {
  const allUsers = useQuery(api.users.allUsersExceptCurrent);
  const startConversation = useMutation(api.chat.startConversation);
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const handleStartConversation = async (userId: Id<'users'>, name: string) => {
    await startConversation({ otherUserId: userId, receiverName: name });
  };
  const runCommand = useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);
  return (
    <>
      <Button
        className={cn('text-muted-foreground', iconOnly && 'h-10', className)}
        onClick={() => setOpen(true)}
        size={'sm'}
        variant="outline"
      >
        <MessageCirclePlusIcon className="size-4" />
        {!iconOnly && 'Start Conversation'}
      </Button>

      <CommandDialog
        className="rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'Search users...'}
        />

        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>.
          </CommandEmpty>
          <CommandGroup heading="Users">
            {allUsers?.length ? (
              allUsers.map((user) => (
                <CommandItem
                  className="flex items-center justify-between gap-2"
                  key={user._id}
                  onSelect={() =>
                    runCommand(() =>
                      handleStartConversation(
                        user._id,
                        user.username || user.name || 'Unknown'
                      )
                    )
                  }
                  value={user.username || user.name || 'Unknown'}
                >
                  <div className="flex items-center gap-2">
                    <Avatar className="size-8">
                      <AvatarImage src={user.avatarUrl || profileImg.src} />
                      <AvatarFallback>{user.name?.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="font-medium capitalize">
                        {user.username}
                      </span>
                      <span className="hidden text-muted-foreground text-sm lg:block">
                        Joined at {format(new Date(user._creationTime), 'PP')}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 pr-5">
                    <BriefcaseBusinessIcon className="size-4 text-muted-foreground" />
                    <span className="capitalize">{user.role}</span>
                  </div>
                </CommandItem>
              ))
            ) : (
              <CommandEmpty>No other users found.</CommandEmpty>
            )}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
