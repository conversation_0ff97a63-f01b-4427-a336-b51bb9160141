'use client';
import { SearchIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import type { TAllMedia } from './media-table';

export default function SearchMediaMobile({
  medias,
  basePath,
}: {
  medias: TAllMedia;
  basePath: string;
}) {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = useState('');

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'm' && (e.metaKey || e.ctrlKey)) {
        if (
          (e.target instanceof HTMLElement && e.target.isContentEditable) ||
          e.target instanceof HTMLInputElement ||
          e.target instanceof HTMLTextAreaElement ||
          e.target instanceof HTMLSelectElement
        ) {
          return;
        }

        e.preventDefault();
        setOpen((op) => !op);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);
  const runCommand = React.useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);

  return (
    <>
      <Button
        className="min-h-10 w-fit bg-background sm:hidden"
        onClick={() => setOpen(true)}
        variant="outline"
      >
        <SearchIcon className="size-5" />
        <span>Search</span>
        <span className="sr-only">Open find</span>
      </Button>
      <CommandDialog
        className="top-[30%] rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'Search media...'}
        />
        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>
          </CommandEmpty>
          <CommandGroup heading="Media">
            {medias.map((media) => (
              <CommandItem
                key={media._id}
                onSelect={() =>
                  runCommand(() =>
                    router.push(`${basePath}/media/${media.slug}`)
                  )
                }
                value={media.title}
              >
                {media.title}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
