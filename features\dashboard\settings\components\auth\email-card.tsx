'use client';

import { type Preloaded, usePreloadedQuery } from 'convex/react';

import type { api } from '@/convex/_generated/api';
import AddNewEmail from './add-new-email';
import OtherEmail from './other-email';

export default function EmailCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);

  if (!user) {
    return null;
  }

  return (
    <div className="flex w-full flex-col rounded-sm ring ring-ring/20 dark:ring-ring/40">
      {/* Header */}
      <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
        <div className="flex flex-col gap-2">
          <h2 className="font-medium text-primary text-xl">Email</h2>
          <p className="font-normal text-primary/60 text-sm">
            Enter the email addresses you want to use to log in with Vercel.
            Your primary email will be used for account-related notifications.
          </p>
        </div>

        {/* Email Row */}

        {user.otherEmails && user.otherEmails.length > 0 && (
          <div className="flex flex-col gap-3">
            {user.otherEmails.map((eml) => (
              <OtherEmail eml={eml} key={eml.email} />
            ))}
          </div>
        )}

        <AddNewEmail />
      </div>

      {/* Footer */}
      <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-5 md:flex-row md:items-center dark:bg-card">
        <p className="font-normal text-primary/60 text-sm">
          Emails must be verified to be able to log in with them or use them as
          your primary email.
        </p>
      </div>
    </div>
  );
}
