'use client';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import { displayNameFormSchema, type TDisplayNameFormValues } from '../schema';

export function DisplayNameCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const updateDisplayName = useMutation(api.users.updateDisplayName);
  const form = useForm<TDisplayNameFormValues>({
    resolver: zod<PERSON><PERSON>olver(displayNameFormSchema),
    defaultValues: { name: user?.name || '' },
  });

  useEffect(() => {
    if (user?.name) {
      form.reset({ name: user.name });
    }
  }, [user?.name, form]);

  if (!user) {
    return null;
  }
  const handleSubmit = (values: TDisplayNameFormValues) => {
    try {
      updateDisplayName({ name: values.name });
      toast.success('Display name updated successfully!');
    } catch {
      toast.error('Failed to update display name.');
    }
  };
  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40 "
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">Display Name</h2>
            <p className="font-normal text-primary/60 text-sm">
              Please enter your full name, or a display name you are comfortable
              with.
            </p>
          </div>
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Display Name</FormLabel>
                <FormControl>
                  <Input
                    autoComplete="off"
                    className="w-fit lg:min-w-sm dark:bg-black"
                    placeholder="Display name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            Please use 32 characters at maximum.
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
