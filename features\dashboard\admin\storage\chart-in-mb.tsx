'use client';

// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import React from 'react';
import { Label, Pie, PieChart } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { api } from '@/convex/_generated/api';

const chartConfig = {
  total: {
    label: 'Total GB',
  },
  images: {
    label: 'Images',
    color: 'var(--chart-1)',
  },
  videos: {
    label: 'Videos',
    color: 'var(--chart-2)',
  },
  audios: {
    label: 'Audios',
    color: 'var(--chart-3)',
  },
  documents: {
    label: 'Documents',
    color: 'var(--chart-4)',
  },
  others: {
    label: 'Others',
    color: 'var(--chart-5)',
  },
} satisfies ChartConfig;

export function StorageChartInMB() {
  const chartData = useQuery(api.admin.getMediaStatsInGb);
  const totalSize = React.useMemo(() => {
    return chartData?.reduce((acc, curr) => acc + curr.total, 0) || 0;
  }, [chartData]);
  if (!chartData) return null;

  return (
    <Card className="flex w-full flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Storage Usage Overview</CardTitle>
        <CardDescription>
          Breakdown of total storage used across all file types
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          className="mx-auto aspect-square max-h-[250px]"
          config={chartConfig}
        >
          <PieChart>
            <ChartTooltip
              content={<ChartTooltipContent hideLabel />}
              cursor={false}
            />
            <Pie
              data={chartData}
              dataKey="total"
              innerRadius={60}
              nameKey="fileType"
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    const isGb = totalSize > 1024;
                    const value = isGb
                      ? (totalSize / 1024).toFixed(2)
                      : totalSize.toFixed(2);
                    return (
                      <text
                        dominantBaseline="middle"
                        textAnchor="middle"
                        x={viewBox.cx}
                        y={viewBox.cy}
                      >
                        <tspan
                          className="fill-foreground font-bold text-3xl"
                          x={viewBox.cx}
                          y={viewBox.cy}
                        >
                          {value}
                        </tspan>
                        <tspan
                          className="fill-muted-foreground"
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                        >
                          {isGb ? 'GB' : 'MB'}
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="max-w-sm text-center text-muted-foreground leading-none">
          <p className="hidden md:block">
            This chart represents the total storage used by files uploaded to
            the system, categorized by file type.
          </p>
          <p className="md:hidden">Total storage used</p>
        </div>
      </CardFooter>
    </Card>
  );
}
