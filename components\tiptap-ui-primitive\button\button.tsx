'use client';

// --- Tiptap UI Primitive ---
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/tiptap-ui-primitive/tooltip';

// --- Lib ---
import { cn, parseShortcutKeys } from '@/lib/tiptap-utils';

import '@/components/tiptap-ui-primitive/button/button-colors.scss';
import '@/components/tiptap-ui-primitive/button/button-group.scss';
import '@/components/tiptap-ui-primitive/button/button.scss';
import React from 'react';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  showTooltip?: boolean;
  tooltip?: React.ReactNode;
  shortcutKeys?: string;
}

export const ShortcutDisplay: React.FC<{ shortcuts: string[] }> = ({
  shortcuts,
}) => {
  if (shortcuts.length === 0) return null;

  return (
    <div>
      {shortcuts.map((key, index) => (
        <React.Fragment key={index}>
          {index > 0 && <kbd>+</kbd>}
          <kbd>{key}</kbd>
        </React.Fragment>
      ))}
    </div>
  );
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      children,
      tooltip,
      showTooltip = true,
      shortcutKeys,
      'aria-label': ariaLabel,
      ...props
    },
    ref
  ) => {
    const shortcuts = React.useMemo(
      () => parseShortcutKeys({ shortcutKeys }),
      [shortcutKeys]
    );

    if (!(tooltip && showTooltip)) {
      return (
        <button
          aria-label={ariaLabel}
          className={cn('tiptap-button cursor-pointer', className)}
          ref={ref}
          {...props}
        >
          {children}
        </button>
      );
    }

    return (
      <Tooltip delay={200}>
        <TooltipTrigger
          aria-label={ariaLabel}
          className={cn('tiptap-button cursor-pointer', className)}
          ref={ref}
          {...props}
        >
          {children}
        </TooltipTrigger>
        <TooltipContent>
          {tooltip}
          <ShortcutDisplay shortcuts={shortcuts} />
        </TooltipContent>
      </Tooltip>
    );
  }
);

Button.displayName = 'Button';

export const ButtonGroup = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<'div'> & {
    orientation?: 'horizontal' | 'vertical';
  }
>(({ className, children, orientation = 'vertical', ...props }, ref) => {
  return (
    <div
      className={cn('tiptap-button-group', className)}
      data-orientation={orientation}
      ref={ref}
      // role="group"
      {...props}
    >
      {children}
    </div>
  );
});
ButtonGroup.displayName = 'ButtonGroup';

export default Button;
