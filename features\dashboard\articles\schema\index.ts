import z from "zod";

export const articleSchema = z.object({
  title: z
    .string()
    .min(3, { message: "Title must be at least 3 characters." })
    .max(160, { message: "Title must not be longer than 160 characters." }),
  description: z.string().optional(),
});
export type TArticleSchema = z.infer<typeof articleSchema>;

export const rejectionMessageSchema = z.object({
  message: z.string().min(1, { message: "Message is required." }),
});
export type TRejectionMessageSchema = z.infer<typeof rejectionMessageSchema>;
