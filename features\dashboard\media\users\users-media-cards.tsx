'use client';
// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';

import { api } from '@/convex/_generated/api';
import AuthorMediaCard from './users-media-card';
export default function UsersMediaCards() {
  const mediaFiles = useQuery(api.media.getApprovedMediaFiles) || [];
  if (!(mediaFiles && Array.isArray(mediaFiles))) {
    return null;
  }
  if (mediaFiles.length === 0) {
    return (
      <div className="col-span-full flex flex-col items-center justify-center gap-2">
        <p className="text-muted-foreground text-sm">No media found.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {mediaFiles.map((media) => (
        <AuthorMediaCard key={media._id} media={media} />
      ))}
    </div>
  );
}
