import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery, preloadQuery } from 'convex/nextjs';
import { redirect } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { SiteFooter } from '@/features/dashboard/shared/_layout/site-footer';
import SiteHeader from '@/features/dashboard/shared/_layout/site-header';
export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await fetchQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  if (!user?.username) {
    return redirect('/onboarding');
  }

  if (!user?.role) {
    return redirect('/onboarding');
  }

  const preloadedUser = await preloadQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );
  return (
    <div className="relative z-10 flex min-h-svh flex-col bg-background">
      <SiteHeader preloadedUser={preloadedUser} />
      <main className="min-h-screen bg-[#fafafa] dark:bg-black">
        {children}
      </main>
      <SiteFooter />
    </div>
  );
}
