import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import { DataTableColumnHeader } from '@/components/custom/data-table-column-header';
import { DragHandle } from '@/components/custom/drag-handle';
import { Badge } from '@/components/ui/badge';
import { capitalize, truncate } from '@/lib/utils';
import StaffTableColumnActions from './column-actions';
import type { TStaff } from './staff-card';
export const staffColumns: ColumnDef<TStaff>[] = [
  {
    id: 'dragHandle',
    header: '',
    cell: () => <DragHandle />,
    meta: {
      label: 'Drag Handle',
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const staffName = (row.getValue('name') as string) ?? '';
      return (
        <Link
          className="flex items-center gap-4 font-medium decoration-dashed underline-offset-4 hover:underline"
          href={'/admin/staff'}
        >
          {truncate(capitalize(staffName), 40)}
        </Link>
      );
    },
    meta: {
      label: 'Name',
    },
  },
  {
    accessorKey: 'username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Username" />
    ),
    cell: ({ row }) => {
      const staffUsername = (row.getValue('username') as string) ?? '';
      return (
        <Link
          className="flex items-center gap-4 text-muted-foreground decoration-dashed underline-offset-4 hover:text-primary hover:underline"
          href={'/admin/staff'}
        >
          {truncate(capitalize(staffUsername), 40)}
        </Link>
      );
    },
    meta: {
      label: 'Username',
    },
  },
  //   email
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      const staffEmail = (row.getValue('email') as string) ?? '';
      return (
        <span className="text-muted-foreground">
          {truncate(staffEmail, 40)}
        </span>
      );
    },
    meta: {
      label: 'Email',
    },
  },
  // role
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" />
    ),
    cell: ({ row }) => {
      const staffRole = (row.getValue('role') as string) ?? '';
      return (
        <span className="text-muted-foreground capitalize">{staffRole}</span>
      );
    },
    meta: {
      label: 'Role',
    },
  },
  {
    accessorKey: 'verified',
    header: 'Verified',
    cell: ({ row }) => {
      const isVerified = row.getValue('verified') as boolean;
      return (
        <Badge variant={isVerified ? 'default' : 'secondary'}>
          {isVerified ? 'Verified' : 'Not Verified'}
        </Badge>
      );
    },
    meta: {
      label: 'Verified',
    },
  },
  {
    accessorKey: '_creationTime',
    header: 'Joined At',
    cell: ({ row }) => {
      return (
        <span className="text-muted-foreground">
          {formatDistanceToNow(new Date(row.getValue('_creationTime')), {
            addSuffix: true,
          })}
        </span>
      );
    },
    meta: {
      label: 'Joined At',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <StaffTableColumnActions row={row} />,
    meta: {
      label: 'Actions',
    },
  },
];
