import React from 'react';

export const Repeat2Icon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        className={className}
        fill="currentColor"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <title>Repeat</title>
        <path
          clipRule="evenodd"
          d="M5.70711 5.29289C5.31658 4.90237 4.68342 4.90237 4.29289 5.29289L1.29289 8.29289C0.902369 8.68342 0.902369 9.31658 1.29289 9.70711C1.68342 10.0976 2.31658 10.0976 2.70711 9.70711L4 8.41421V16C4 16.7956 4.31607 17.5587 4.87868 18.1213C5.44129 18.6839 6.20435 19 7 19H13C13.5523 19 14 18.5523 14 18C14 17.4477 13.5523 17 13 17H7C6.73478 17 6.48043 16.8946 6.29289 16.7071C6.10536 16.5196 6 16.2652 6 16V8.41421L7.29289 9.70711C7.68342 10.0976 8.31658 10.0976 8.70711 9.70711C9.09763 9.31658 9.09763 8.68342 8.70711 8.29289L5.70711 5.29289ZM15.2929 14.2929C15.6834 13.9024 16.3166 13.9024 16.7071 14.2929L18 15.5858V8C18 7.73478 17.8946 7.48043 17.7071 7.29289C17.5196 7.10536 17.2652 7 17 7H11C10.4477 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H17C17.7957 5 18.5587 5.31607 19.1213 5.87868C19.6839 6.44129 20 7.20435 20 8V15.5858L21.2929 14.2929C21.6834 13.9024 22.3166 13.9024 22.7071 14.2929C23.0976 14.6834 23.0976 15.3166 22.7071 15.7071L19.7071 18.7071C19.3166 19.0976 18.6834 19.0976 18.2929 18.7071L15.2929 15.7071C14.9024 15.3166 14.9024 14.6834 15.2929 14.2929Z"
          fill="currentColor"
          fillRule="evenodd"
        />
      </svg>
    );
  }
);

Repeat2Icon.displayName = 'Repeat2Icon';
