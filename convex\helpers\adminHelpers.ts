import { requireUser } from "../users";
import { ROLES } from "../utils/constants";
import { QueryCtx } from "../_generated/server";

export async function requireAdmin(ctx: QueryCtx) {
  const userId = await requireUser(ctx);
  const user = await ctx.db.get(userId);
  if (!user) return { success: false, error: "User not found." };
  if (user.role !== ROLES.ADMIN)
    return { success: false, error: "Unauthorized." };
  return { success: true, user };
}
