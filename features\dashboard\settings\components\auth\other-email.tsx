import { useMutation } from 'convex/react';
import { EllipsisIcon } from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';

type TEmailItem = {
  email: string;
  isVerified: boolean;
  isPrimary: boolean;
};
export default function OtherEmail({ eml }: { eml: TEmailItem }) {
  const removeUserEmail = useMutation(api.users.removeUserEmail);
  const { email, isVerified, isPrimary } = eml;

  const handleRemoveEmail = () => {
    try {
      if (isPrimary) {
        toast.error('Cannot remove primary email.');
        return;
      }
      removeUserEmail({ email });
      toast.success('Email removed successfully!');
    } catch {
      toast.error('Failed to remove email.');
    }
  };
  return (
    <div className="flex justify-between rounded-sm border border-border p-3">
      <div className="flex items-center gap-2">
        <p className="font-normal text-sm">{email}</p>
        {isVerified ? (
          <Badge className="bg-blue-50 text-blue-600">Verified</Badge>
        ) : (
          <Badge className="bg-red-50 text-red-600">Not Verified</Badge>
        )}
        {isPrimary ? (
          <Badge className="bg-green-50 text-green-600">Primary</Badge>
        ) : null}
      </div>
      {/* Actions */}
      <Popover>
        <PopoverTrigger asChild>
          <Button size="icon" variant="ghost">
            <EllipsisIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="absolute right-4 w-56 p-2">
          <div className="flex flex-col">
            {!isVerified && (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                variant="ghost"
              >
                Verify
              </Button>
            )}
            {isVerified && !isPrimary && (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                variant="ghost"
              >
                Make primary
              </Button>
            )}
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              disabled={isPrimary}
              onClick={handleRemoveEmail}
              variant="ghost"
            >
              Remove
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
