'use client';

import { Emoji, gitHubEmojis } from '@tiptap/extension-emoji';
import { Highlight } from '@tiptap/extension-highlight';
import { Image } from '@tiptap/extension-image';
import { TaskItem, TaskList } from '@tiptap/extension-list';
import { Subscript } from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { TextAlign } from '@tiptap/extension-text-align';
import { Color, TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { CharacterCount, Placeholder, Selection } from '@tiptap/extensions';
import { EditorContent, EditorContext, useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { createPortal } from 'react-dom';
// --- Tiptap Core Extensions ---
import { UiState } from '@/components/tiptap-extension/ui-state-extension';
import { HorizontalRule } from '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node-extension';
// --- Tiptap Node ---
import { ImageUploadNode } from '@/components/tiptap-node/image-upload-node/image-upload-node-extension';
// --- UI Primitives ---
import '@/components/tiptap-node/blockquote-node/blockquote-node.scss';
import '@/components/tiptap-node/code-block-node/code-block-node.scss';
import '@/components/tiptap-node/heading-node/heading-node.scss';
import '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node.scss';
import '@/components/tiptap-node/image-node/image-node.scss';
import '@/components/tiptap-node/list-node/list-node.scss';
import '@/components/tiptap-node/paragraph-node/paragraph-node.scss';

// --- Icons ---
// --- Components ---
// --- Tiptap UI ---
import { DragContextMenu } from '@/components/tiptap-ui/drag-context-menu';
import { EmojiDropdownMenu } from '@/components/tiptap-ui/emoji-dropdown-menu';
import { SlashDropdownMenu } from '@/components/tiptap-ui/slash-dropdown-menu';
// --- Hooks ---

// --- Lib ---
import { handleImageUpload, MAX_FILE_SIZE } from '@/lib/tiptap-utils';

// --- Styles ---
import '@/components/tiptap-templates/simple/simple-editor.scss';

import React from 'react';

import { cn } from '@/lib/utils';
import { NotionEditorHeader } from './notion-like-editor-header';
import { MobileToolbar } from './notion-like-editor-mobile-toolbar';
import { NotionToolbarFloating } from './notion-like-editor-toolbar-floating';

/**
 * EditorContent component that renders the actual editor
 */
export function EditorContentArea() {
  const { editor } = React.useContext(EditorContext);

  if (!editor) {
    return null;
  }

  return (
    <EditorContent
      className="simple-editor-content z-0 mx-auto flex h-full w-full max-w-6xl flex-col overflow-auto"
      editor={editor}
      role="presentation"
      style={{
        cursor: editor.view.dragging ? 'grabbing' : 'auto',
      }}
    >
      <DragContextMenu />
      <EmojiDropdownMenu />
      <SlashDropdownMenu />
      <NotionToolbarFloating />

      {createPortal(<MobileToolbar />, document.body)}
    </EditorContent>
  );
}

export type EditorProviderProps = {
  placeholder: string;
  content: string;
  words: number;
  onChange?: (content: string, words: number) => void;
  editable?: boolean;
  className?: string;
  id: string;
  disabled?: boolean;
};
export function SimpleEditor({
  placeholder,
  content: initialContent,
  onChange,
  editable = false,
  className,
  disabled,
}: EditorProviderProps) {
  const editor = useEditor({
    editable: editable && !disabled,
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        'aria-label': 'Main content area, start typing to enter text.',
        class: 'simple-editor',
      },
    },
    extensions: [
      StarterKit.configure({
        horizontalRule: false,
        link: {
          openOnClick: false,
          enableClickSelection: true,
        },
      }),
      CharacterCount,
      Placeholder.configure({
        placeholder,
        emptyNodeClass: 'is-empty with-slash',
      }),
      HorizontalRule,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      TaskList,
      TaskItem.configure({ nested: true }),
      Highlight.configure({ multicolor: true }),
      Image,
      Emoji.configure({
        emojis: gitHubEmojis.filter(
          (emoji) => !emoji.name.includes('regional')
        ),
        forceFallbackImages: true,
      }),
      Color,
      TextStyle,
      Typography,
      Superscript,
      Subscript,
      Selection,
      UiState,
      ImageUploadNode.configure({
        accept: 'image/*',
        maxSize: MAX_FILE_SIZE,
        limit: 3,
        upload: handleImageUpload,
        // onError: (error) => console.error('Upload failed:', error),
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor: updatedEditor }) => {
      if (onChange)
        onChange(
          updatedEditor.getHTML(),
          updatedEditor.storage.characterCount.words()
        );
    },
  });

  return (
    <div className={cn('relative min-h-screen w-full ', className)}>
      <EditorContext.Provider value={{ editor }}>
        {editable && <NotionEditorHeader />}
        <EditorContentArea />
      </EditorContext.Provider>
    </div>
  );
}
