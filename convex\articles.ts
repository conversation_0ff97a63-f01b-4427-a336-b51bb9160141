import { v } from "convex/values";
import { api, internal } from "./_generated/api";
import { internalMutation, mutation, query } from "./_generated/server";
import { canUpdateArticle, generateSlug } from "./helpers/articleHelper";
import { requireUser } from "./users";
import { DOCS_STATUS, ROLES } from "./utils/constants";
import { Id } from "./_generated/dataModel";

export const createArticle = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    content: v.optional(v.string()),
    words: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "User not found." };
      }
      if (user.role !== ROLES.ADMIN && user.role !== ROLES.AUTHOR) {
        return { success: false, error: "Unauthorized." };
      }
      const slug = await generateSlug(ctx, args.title);

      const articleId = await ctx.db.insert("articles", {
        ...args,
        slug,
        userId,
        status:
          user.role === ROLES.ADMIN ? DOCS_STATUS.APPROVED : DOCS_STATUS.DRAFT,

        isFavorite: false,
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: args.title,
        docStatus:
          user.role === ROLES.ADMIN ? DOCS_STATUS.APPROVED : DOCS_STATUS.DRAFT,
        action: "created" as const,
      });

      return { success: true, data: { id: articleId, title: args.title } };
    } catch (err) {
      console.error("createArticle error:", err);
      return { success: false, error: "Failed to create article." };
    }
  },
});

export const updateArticle = mutation({
  args: {
    id: v.id("articles"),
    title: v.string(),
    description: v.optional(v.string()),
    content: v.optional(v.string()),
    words: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // ✅ central check
      const { allowed, error, article } = await canUpdateArticle(ctx, args.id);
      if (!allowed || !article) {
        return { success: false, error };
      }

      const slug = await generateSlug(ctx, args.title, args.id);

      await ctx.db.patch(args.id, {
        title: args.title,
        description: args.description,
        content: args.content,
        words: args.words,
        slug,
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: article.status,
        action: "updated" as const,
      });

      return { success: true, data: { id: args.id, title: args.title, slug } };
    } catch (err) {
      console.error("updateArticle error:", err);
      return { success: false, error: "Failed to update article." };
    }
  },
});

export const getArticles = query({
  args: {},
  handler: async (ctx) => {
    try {
      const userId = await requireUser(ctx);
      const articles = await ctx.db
        .query("articles")
        .withIndex("by_userId", (q) => q.eq("userId", userId))
        .collect();
      return articles;
    } catch {
      return { success: false, error: "Failed to fetch articles." };
    }
  },
});

export const getArticle = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "User not found." };
      }
      const article = await ctx.db
        .query("articles")
        .withIndex("by_slug", (q) => q.eq("slug", args.slug))
        .unique();
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      const isOwner = article.userId === userId;
      const isAdmin = user.role === ROLES.ADMIN;

      if (!isOwner && !isAdmin) {
        return { success: false, error: "Unauthorized." };
      }

      return article;
    } catch {
      return { success: false, error: "Failed to fetch article." };
    }
  },
});

export const deleteArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const { allowed, error, article } = await canUpdateArticle(ctx, args.id);
      if (!allowed || !article) {
        return { success: false, error };
      }
      await ctx.db.patch(article._id, {
        status: "deleted",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "deleted" as const,
      });
      await ctx.scheduler.runAfter(
        1000 * 60 * 60 * 24 * 7, // 7 days
        internal.articles.removeDeletedArticles,
        {}
      );
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete article." };
    }
  },
});

export const restoreArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const { allowed, error, article } = await canUpdateArticle(ctx, args.id);
      if (!allowed || !article) {
        return { success: false, error };
      }
      await ctx.db.patch(article._id, {
        status: "draft",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "restored" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to restore article." };
    }
  },
});

export const toggleFavoriteArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }

      await ctx.db.patch(args.id, {
        isFavorite: !article.isFavorite,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: article.isFavorite
          ? ("removed-from-favorite" as const)
          : ("added-to-favorite" as const),
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to toggle favorite article." };
    }
  },
});

export const stageArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "staged",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "staged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to stage article." };
    }
  },
});
export const unStageArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "draft",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "unstaged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to unstage article." };
    }
  },
});

export const removeDeletedArticles = internalMutation({
  args: {},
  handler: async (ctx) => {
    const articles = await ctx.db
      .query("articles")
      .withIndex("by_status", (q) => q.eq("status", "deleted"))
      .collect();
    for (const article of articles) {
      await ctx.db.delete(article._id);
    }
  },
});

export const searchArticles = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const articles = await ctx.db
        .query("articles")
        .withSearchIndex("search_title", (q) => q.search("title", query))
        .collect();
      return articles;
    } catch {
      return { success: false, error: "Failed to search articles." };
    }
  },
});

export const getArticlesAnalytics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await requireUser(ctx);

    const articles = await ctx.db
      .query("articles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .collect();

    return articles.map((article) => ({
      date: new Date(article.updatedAt || article._creationTime)
        .toISOString()
        .split("T")[0], // 'YYYY-MM-DD'
      status: article.status,
    }));
  },
});

export const getTotalArticlesByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("staged"),
      v.literal("approved"),
      v.literal("published"),
      v.literal("deleted")
    ),
  },
  handler: async (ctx, { status }) => {
    const userId = await requireUser(ctx);

    const total = await ctx.db
      .query("articles")
      .withIndex("by_status_userId", (q) =>
        q.eq("status", status).eq("userId", userId)
      )
      .collect()
      .then((articles) => articles.length);

    return total;
  },
});

// get all articles which are published
export const getPublishedArticles = query({
  args: {},
  handler: async (ctx) => {
    const articles = await ctx.db
      .query("articles")
      .withIndex("by_status", (q) => q.eq("status", "published"))
      .collect();
    if (!articles) return [];

    return await Promise.all(
      articles.map(async (article) => {
        let groupName: string | null = null;

        if (article.groupId) {
          const group = await ctx.db.get(article.groupId as Id<"groups">);
          groupName = group?.name ?? null;
        }
        return {
          title: article.title,
          slug: article.slug,
          description: article.description,
          content: article.content,
          words: article.words,
          coverImage: article.coverImage,
          status: article.status,
          group: groupName,
          authorId: article.userId,
          publishedAt: article.updatedAt || article._creationTime,
        };
      })
    );
  },
});
