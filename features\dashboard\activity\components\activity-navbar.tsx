'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

const activityTypes = [
  { id: 'all', label: 'All' },
  { id: 'created', label: 'Created' },
  { id: 'updated', label: 'Updated' },
  { id: 'added-to-favorite', label: 'Added to Favorite' },
  { id: 'removed-from-favorite', label: 'Removed from Favorite' },
  { id: 'deleted', label: 'Deleted' },
  { id: 'restored', label: 'Restored' },
  { id: 'staged', label: 'Staged' },
  { id: 'unstaged', label: 'Unstaged' },
  { id: 'unapproved', label: 'Unapproved' },
  { id: 'approved', label: 'Approved' },
  { id: 'rejected', label: 'Rejected' },
  { id: 'published', label: 'Published' },
  { id: 'unpublished', label: 'Unpublished' },
  { id: 'group-added', label: 'Group Added' },
  { id: 'group-removed', label: 'Group Removed' },
];

export function ActivityLinksNavbar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [checkedTypes, setCheckedTypes] = useState<string[]>([]);

  // Initialize state from URL
  useEffect(() => {
    const types = searchParams.getAll('type');
    setCheckedTypes(types.length ? types : ['all']);
  }, [searchParams]);

  const updateUrl = (types: string[]) => {
    const params = new URLSearchParams();
    for (const t of types) {
      params.append('type', t);
    }
    router.push(`${pathname}${types.length ? `?${params}` : ''}`);
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    let newTypes: string[] = [];

    if (id === 'all') {
      newTypes = checked ? ['all'] : [];
    } else if (checked) {
      // Remove "all" if it exists
      newTypes = [...checkedTypes.filter((t) => t !== 'all'), id];
    } else {
      newTypes = checkedTypes.filter((t) => t !== id);
    }

    setCheckedTypes(newTypes);
    updateUrl(newTypes);
  };

  return (
    <div className="mr-12 hidden w-full max-w-3xs px-2 md:px-4 lg:block">
      <div className="sticky top-16">
        <Accordion collapsible type="single">
          <AccordionItem value="type-filter">
            <AccordionTrigger className="cursor-pointer text-muted-foreground hover:text-primary hover:no-underline">
              Type
            </AccordionTrigger>
            <ScrollArea className="h-[calc(100vh-20rem)]">
              <AccordionContent className="mt-10 flex flex-col gap-5">
                {activityTypes.map((type) => (
                  <div key={type.id}>
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={checkedTypes.includes(type.id)}
                        className="cursor-pointer"
                        id={type.id}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(type.id, Boolean(checked))
                        }
                      />
                      <Label
                        // if checked text-primary else text-muted-foreground
                        className={cn(
                          'cursor-pointer font-normal',
                          checkedTypes.includes(type.id)
                            ? 'text-primary'
                            : 'text-muted-foreground'
                        )}
                        htmlFor={type.id}
                      >
                        {type.label}
                      </Label>
                    </div>
                  </div>
                ))}
              </AccordionContent>
            </ScrollArea>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
