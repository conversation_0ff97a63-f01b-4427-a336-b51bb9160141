import { ArmchairIcon } from 'lucide-react';
import Link from 'next/link';
import { ThemeSwitcher } from '@/components/mode/theme-switcher';
import { Button } from '@/components/ui/button';
import { footerLinks } from '@/config/dashboard';
import { cn } from '@/lib/utils';

export function SiteFooter() {
  return (
    <footer className=" border-t bg-background p-6 pt-5 ">
      <div className="flex flex-col gap-3">
        <div className="flex flex-col justify-between gap-x-4 gap-y-6 md:flex-row md:items-center">
          <div className="flex flex-col gap-6 md:flex-row md:gap-4">
            <Link href="/">
              <ArmchairIcon className="size-6" />
            </Link>
            <ul className="m-0 grid w-full list-none grid-cols-2 items-start gap-4 p-0 md:flex md:items-center">
              {footerLinks.map((item) => (
                <Link
                  className="text-muted-foreground text-sm hover:text-primary"
                  href={item.href}
                  key={item.label}
                >
                  {item.label}
                </Link>
              ))}
            </ul>
          </div>

          <div className="flex items-center justify-between gap-2">
            <Button variant={'ghost'}>
              <span className="relative flex size-2">
                <span
                  className={cn(
                    'absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-500 opacity-75'
                  )}
                />
                <span
                  className={cn(
                    'relative inline-flex h-2 w-2 rounded-full bg-blue-500'
                  )}
                />
              </span>
              <Link className="text-blue-600 dark:text-blue-500" href={'/'}>
                All Sytem normal
              </Link>
            </Button>
            <ThemeSwitcher />
            {/* <ModeSwitcher /> */}
          </div>
        </div>

        <div className="text-muted-foreground text-xs tracking-tight">
          © 2025, Better Flow. Powered by{' '}
          <Link
            className="font-medium text-blue-600 underline-offset-2 hover:underline dark:text-blue-500"
            href={'https://www.rathon-rw.com'}
            rel="noopener noreferrer"
            target="_blank"
          >
            Rathon
          </Link>
        </div>
      </div>
    </footer>
  );
}
