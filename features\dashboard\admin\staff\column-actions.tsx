import type { Row } from '@tanstack/react-table';
import { useMutation } from 'convex/react';
import { MoreHorizontal } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import type { TStaff } from './staff-card';
export default function StaffTableColumnActions<TData>({
  row,
}: {
  row: Row<TData>;
}) {
  const staff = row.original as TStaff;
  const verifyStaffMember = useMutation(api.admin.verifyStaffMember);
  const unverifyStaffMember = useMutation(api.admin.unverifyStaffMember);
  if (!staff || 'success' in staff) {
    return null;
  }
  const handleVerifyStaffMember = () => {
    try {
      verifyStaffMember({ id: staff._id });
      toast.success('Staff verified successfully!');
    } catch {
      toast.error('Failed to verify staff.');
    }
  };
  const handleUnverifyStaffMember = () => {
    try {
      unverifyStaffMember({ id: staff._id });
      toast.success('Staff unverified successfully!');
    } catch {
      toast.error('Failed to unverify staff.');
    }
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
          variant="ghost"
        >
          <MoreHorizontal className="size-4" />
          <span className="sr-only">Open staff actions</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {staff.verified ? (
          <DropdownMenuItem onClick={handleUnverifyStaffMember}>
            Unverify
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem onClick={handleVerifyStaffMember}>
            Verify
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
