'use client';
import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { ChevronLeftIcon } from 'lucide-react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import TextareaAutosize from 'react-textarea-autosize';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
// import { SimpleEditor } from '@/components/tiptap-templates/simple/simple-editor';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { api } from '@/convex/_generated/api';
import {
  articleEditorFormSchema,
  type TArticleEditorFormSchema,
} from './schema';

const SimpleEditor = dynamic(
  () =>
    import('@/components/tiptap-templates/simple/simple-editor').then(
      (mod) => ({ default: mod.SimpleEditor })
    ),
  {
    ssr: false, // Disable server-side rendering for this component
  }
);

export default function NewEditorForm() {
  const pathname = usePathname();
  const router = useRouter();
  const basePath = `/${pathname.split('/')[1]}`;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createArticleEditor = useMutation(api.articles.createArticle);
  const form = useForm<TArticleEditorFormSchema>({
    resolver: zodResolver(articleEditorFormSchema),
    defaultValues: {
      title: '',
      description: '',
      content: '',
      words: 0,
    },
  });

  const onSubmit = useCallback(
    async (formData: TArticleEditorFormSchema) => {
      setIsSubmitting(true);

      try {
        const createPromise = createArticleEditor(formData);

        toast.promise(createPromise, {
          loading: 'Creating article...',
          success: (res) =>
            `Article "${res?.data?.title ?? 'Untitled'}" created successfully.`,
          error: 'Failed to create article. Please try again.',
        });
        const response = await createPromise;
        if (response?.success) {
          router.push(`/journalist/editor/${response?.data?.id}`);
        }
      } catch {
        toast.error('An unexpected error occurred. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    },
    [createArticleEditor, router]
  );

  // Memoize editor change handler for performance
  const handleEditorChange = useCallback(
    (html: string, words: number) => {
      form.setValue('content', html);
      form.setValue('words', words);
    },
    [form]
  );

  return (
    <Form {...form}>
      <form
        className="mx-auto mt-10 flex w-full max-w-6xl flex-col gap-10"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="flex items-center justify-between">
          <Button asChild type="button" variant="ghost">
            <Link className="group" href={`${basePath}`}>
              <ChevronLeftIcon className="mr-2 size-4 transition-all duration-300 ease-in-out group-hover:translate-x-1" />
              Back
            </Link>
          </Button>
          <Button
            className="w-fit cursor-pointer justify-end"
            disabled={isSubmitting}
            type="submit"
          >
            {isSubmitting ? <Spinner text="Saving..." /> : 'Save'}
          </Button>
        </div>

        <div className="flex flex-col gap-4">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Title</FormLabel>
                <FormControl>
                  <TextareaAutosize
                    className="w-full resize-none appearance-none overflow-hidden bg-transparent font-bold text-3xl focus:outline-0"
                    disabled={isSubmitting}
                    placeholder="Untitled"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Description</FormLabel>
                <FormControl>
                  <TextareaAutosize
                    className="w-full resize-none appearance-none overflow-hidden bg-transparent text-base text-muted-foreground tracking-tight focus:outline-0"
                    disabled={isSubmitting}
                    placeholder="Type your descriptio here... (optional)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Content</FormLabel>
                <FormControl>
                  <SimpleEditor
                    content={field.value ?? ''}
                    disabled={isSubmitting}
                    editable={true}
                    id={''}
                    onChange={handleEditorChange}
                    placeholder="Start writing..."
                    words={0}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}
