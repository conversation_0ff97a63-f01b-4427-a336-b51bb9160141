import { Label } from '@/components/ui/label';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

type TabItem = {
  value: string;
  label: string;
  component: React.ReactNode;
};

type TabbedViewProps = {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
  tablistClassName?: string;
  tabContentClassName?: string;
};

export function TabsView({
  tabs,
  defaultValue,
  className,
  tablistClassName,
  tabContentClassName,
}: TabbedViewProps) {
  const initialValue = defaultValue || tabs[0]?.value || '';

  return (
    <Tabs
      className={cn('flex w-full flex-col gap-6', className)}
      defaultValue={initialValue}
    >
      <div className="flex items-center justify-between border-b">
        <Label className="sr-only" htmlFor="view-selector">
          View
        </Label>
        <ScrollArea className="w-[90vw]">
          <TabsList
            className={cn(
              'mt-6 flex w-full max-w-xl',
              'w-full justify-start rounded-none bg-transparent p-0',
              tablistClassName
            )}
          >
            {tabs.map((tab) => (
              <TabsTrigger
                className={tabTriggerBaseClass}
                key={tab.value}
                value={tab.value}
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <ScrollBar className="h-1.5" orientation="horizontal" />
        </ScrollArea>
      </div>

      {tabs.map((tab) => (
        <TabsContent
          className={tabContentClassName}
          key={tab.value}
          value={tab.value}
        >
          {tab.component}
        </TabsContent>
      ))}
    </Tabs>
  );
}

export const tabTriggerBaseClass = cn(
  '@container/main flex gap-1',
  'relative h-9 rounded-none border-0 border-b-2 border-b-transparent bg-transparent px-4 pt-2 pb-3 ',
  'text-muted-foreground data-[state=active]:border-b-primary data-[state=active]:text-foreground',
  'data-[state=active]:bg-transparent dark:data-[state=active]:border-b-white dark:data-[state=active]:bg-transparent',
  'shadow-none transition-none data-[state=active]:shadow-none',
  'cursor-pointer focus-visible:outline focus-visible:outline-2 focus-visible:outline-primary'
);
