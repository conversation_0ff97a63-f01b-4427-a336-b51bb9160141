import type { Metadata } from 'next';
import BackButton from '@/components/custom/latest-page-link';
import FuzzyText from '@/components/ui/fuzzy-text';

export const metadata: Metadata = {
  title: 'Not Found',
  description: 'Not Found',
};

export default function NotFound() {
  return (
    <div className="flex h-screen flex-col items-center justify-center gap-6 bg-white pb-6">
      <div className="hidden md:block">
        <FuzzyText
          baseIntensity={0.2}
          enableHover={true}
          fontSize={300}
          hoverIntensity={0.1}
        >
          404
        </FuzzyText>
      </div>
      <div className=" md:hidden">
        <FuzzyText
          baseIntensity={0.2}
          enableHover={true}
          fontSize={150}
          hoverIntensity={0.1}
        >
          404
        </FuzzyText>
      </div>
      <div className="hidden md:block">
        <FuzzyText
          baseIntensity={0.2}
          enableHover={true}
          fontSize={100}
          hoverIntensity={0.1}
        >
          Not Found
        </FuzzyText>
      </div>
      <div className="md:hidden">
        <FuzzyText
          baseIntensity={0.2}
          enableHover={true}
          fontSize={50}
          hoverIntensity={0.1}
        >
          Not Found
        </FuzzyText>
      </div>
      <div className="flex flex-col items-center justify-center gap-4 md:flex-row">
        <p className="text-center text-muted-foreground">
          Could not find requested resource
        </p>

        <BackButton className="dark:bg-black dark:text-white dark:hover:bg-black/80" />
      </div>
    </div>
  );
}
