// 'use client';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { useMutation } from 'convex/react';
// import { useEffect, useState } from 'react';
// import { useForm } from 'react-hook-form';
// import { toast } from 'sonner';
// import { Spinner } from '@/components/custom/spinner';
// import { Button, buttonVariants } from '@/components/ui/button';
// import {
//   Dialog,
//   DialogClose,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from '@/components/ui/dialog';
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from '@/components/ui/form';
// import { Input } from '@/components/ui/input';
// import { api } from '@/convex/_generated/api';
// import type { Id } from '@/convex/_generated/dataModel';
// import { cn } from '@/lib/utils';
// import { columnSchema, type TColumnSchema } from './schema';

// interface CreateOrUpdateColumnProps {
//   openDialog?: boolean;
//   setOpenDialog?: (open: boolean) => void;
//   title?: string;
//   id?: Id<'boardColumns'>;
//   boardId: Id<'boards'>;
// }

// export default function CreateOrUpdateColumn({
//   openDialog,
//   setOpenDialog,
//   title,
//   id,
//   boardId,
// }: CreateOrUpdateColumnProps) {
//   const isEditMode = Boolean(id);
//   const createColumnMutation = useMutation(api.board.createColumn);
//   const updateColumnMutation = useMutation(api.board.updateColumn);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [internalOpen, setInternalOpen] = useState(false);

//   const open = openDialog ?? internalOpen;
//   const setOpen = setOpenDialog ?? setInternalOpen;

//   const form = useForm<TColumnSchema>({
//     resolver: zodResolver(columnSchema),
//     defaultValues: {
//       title: title || '',
//     },
//   });
//   useEffect(() => {
//     if (open) {
//       form.reset({
//         title: title || '',
//       });
//     }
//   }, [open, title, form]);

//   const handleSubmit = async (data: TColumnSchema) => {
//     setIsSubmitting(true);
//     setOpen(false);

//     try {
//       if (isEditMode) {
//         if (!id) {
//           toast.error('Failed to get column id.');
//           return;
//         }
//         const result = await updateColumnMutation({
//           id,
//           ...data,
//         });
//         if (result?.success) {
//           form.reset();
//           toast.success('Column updated successfully!');
//         } else {
//           toast.error(result.error);
//         }
//       } else {
//         const result = await createColumnMutation({
//           boardId,
//           ...data,
//         });
//         if (result?.success) {
//           form.reset();
//           toast.success('Column created successfully!');
//         } else {
//           toast.error(result.error);
//         }
//       }
//     } catch {
//       toast.error('Something went wrong.');
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   return (
//     <Dialog onOpenChange={setOpen} open={open}>
//       {!isEditMode && (
//         <DialogTrigger
//           className={cn(
//             buttonVariants({ variant: 'default', size: 'lg' }),
//             'cursor-pointer rounded-sm px-3'
//           )}
//         >
//           Add New Column
//         </DialogTrigger>
//       )}

//       <DialogContent className="flex w-full max-w-md flex-col">
//         <DialogHeader className="sr-only">
//           <DialogTitle>
//             {isEditMode ? 'Update' : 'Create New'} Column
//           </DialogTitle>
//         </DialogHeader>
//         <Form {...form}>
//           <form
//             className="mt-6 space-y-4"
//             onSubmit={form.handleSubmit(handleSubmit)}
//           >
//             <FormField
//               control={form.control}
//               name="title"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Title</FormLabel>
//                   <FormControl>
//                     <Input placeholder="Title" {...field} />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             <Button className="w-full" disabled={isSubmitting} type="submit">
//               {isSubmitting ? (
//                 <Spinner text="Submitting..." />
//               ) : isEditMode ? (
//                 'Update Column'
//               ) : (
//                 'Create Column'
//               )}
//             </Button>
//           </form>
//         </Form>
//         <DialogClose />
//       </DialogContent>
//     </Dialog>
//   );
// }
