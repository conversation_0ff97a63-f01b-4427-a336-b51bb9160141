'use client';
import type { FunctionReturnType } from 'convex/server';
import { useEffect } from 'react';
import type { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { useChatScroll } from '@/hooks/use-chat-scroll';
import { ChatMessageItem } from './chat-massage-item';

type TMessage = FunctionReturnType<typeof api.chat.getConversation>;
export default function ChatMessages({
  messages,
  currentUserId,
}: {
  messages: TMessage;
  currentUserId: Id<'users'>;
}) {
  const { containerRef, scrollToBottom } = useChatScroll();

  useEffect(() => {
    scrollToBottom();
  }, [scrollToBottom]);
  return (
    <div className="px-6">
      {messages.map((message) => {
        const isOwnMessage = message.senderId === currentUserId;
        const showHeader = true;
        return (
          <ChatMessageItem
            isOwnMessage={isOwnMessage}
            key={message._id}
            message={message}
            showHeader={showHeader}
          />
        );
      })}
      {messages.length === 0 ? (
        <div className="text-center text-muted-foreground text-sm">
          {' '}
          No messages yet. Start the conversation!
        </div>
      ) : null}
      <div ref={containerRef} />
    </div>
  );
}
