'use client';

import { CardSimIcon, MoonIcon, SunDimIcon } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

export function ModeSwitcher() {
  const { setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const modes = [
    {
      value: 'system',
      icon: <CardSimIcon className="size-4" />,
      label: 'System',
    },
    { value: 'light', icon: <SunDimIcon className="size-4" />, label: 'Light' },
    { value: 'dark', icon: <MoonIcon className="size-4" />, label: 'Dark' },
  ];
  if (!mounted) {
    return null;
  }
  const handleThemeChange = (
    e: React.MouseEvent<HTMLButtonElement>,
    value: string
  ) => {
    e.preventDefault();
    setTheme(value);
  };
  return (
    <div className="flex w-fit items-center rounded-full ring-1 ring-ring/30">
      {modes.map((mode) => (
        <button
          aria-label={mode.label}
          className={cn(
            'cursor-pointer rounded-full p-1 text-muted-foreground transition-colors',
            resolvedTheme === mode.value && 'text-primary ring-1 ring-ring/30'
          )}
          key={mode.value}
          onClick={(e) => handleThemeChange(e, mode.value)}
          title={mode.label}
          type="button"
        >
          {mode.icon}
        </button>
      ))}
    </div>
  );
}
