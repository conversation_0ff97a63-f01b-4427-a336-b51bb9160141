import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/convex/_generated/api';
import type { TMessage } from './chat-massage-item';

//  schema
const FormSchema = z.object({
  content: z.string().min(2, {
    message: 'Message must be at least 2 characters.',
  }),
});

type TEditMessageFormValues = z.infer<typeof FormSchema>;
export function EditMessageForm({
  message,
  openDialog,
  setOpenDialog,
}: {
  message: TMessage;
  openDialog: boolean;
  setOpenDialog: (open: boolean) => void;
}) {
  const editMessage = useMutation(api.chat.editMessage);
  const [isProcessing, setIsProcessing] = useState(false);
  const form = useForm<TEditMessageFormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      content: message.content,
    },
  });
  const onSubmit = async (data: TEditMessageFormValues) => {
    try {
      setIsProcessing(true);
      await editMessage({ messageId: message._id, message: data.content });
      toast.success('Message edited.');
      setOpenDialog(false);
    } catch {
      toast.error('Could not edit message.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpenDialog} open={openDialog}>
      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>Edit Message</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            className="flex flex-col gap-4"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea
                      className="resize-none"
                      placeholder="Type your message..."
                      {...field}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <Button disabled={isProcessing} type="submit">
              {isProcessing ? 'Loading...' : 'Edit'}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
