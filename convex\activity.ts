// activity function
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ActivityTarget, actionEnum } from "./schema";
import { requireUser } from "./users";

export const createActivity = mutation({
  args: {
    target: ActivityTarget,
    docTitle: v.string(),
    docStatus: v.string(),
    action: actionEnum,
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      await ctx.db.insert("activities", {
        ...args,
        userId,
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to create activity." };
    }
  },
});

export const getActivities = query({
  args: {},
  handler: async (ctx) => {
    try {
      const userId = await requireUser(ctx);
      const activities = await ctx.db
        .query("activities")
        .withIndex("by_userId", (q) => q.eq("userId", userId))
        .order("desc")
        .collect();
      return activities;
    } catch {
      return { success: false, error: "Failed to fetch activities." };
    }
  },
});
