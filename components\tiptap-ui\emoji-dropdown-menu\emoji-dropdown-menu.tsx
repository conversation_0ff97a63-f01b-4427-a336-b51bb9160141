'use client';

import type { EmojiItem } from '@tiptap/extension-emoji';
import type { Editor, Range } from '@tiptap/react';
import React from 'react';
import {
  EmojiMenuItem,
  getFilteredEmojis,
} from '@/components/tiptap-ui/emoji-menu';
import { ButtonGroup } from '@/components/tiptap-ui-primitive/button';
import { Card, CardBody } from '@/components/tiptap-ui-primitive/card';
// --- Tiptap UI ---
import type {
  SuggestionItem,
  SuggestionMenuProps,
  SuggestionMenuRenderProps,
} from '@/components/tiptap-ui-utils/suggestion-menu';
import { SuggestionMenu } from '@/components/tiptap-ui-utils/suggestion-menu';

export type EmojiDropdownMenuProps = Omit<
  SuggestionMenuProps,
  'items' | 'children'
>;

export const EmojiDropdownMenu = (props: EmojiDropdownMenuProps) => {
  return (
    <SuggestionMenu
      char=":"
      decorationClass="tiptap-emoji-decoration"
      items={getSuggestionItems}
      pluginKey="emojiDropdownMenu"
      selector="tiptap-emoji-dropdown-menu"
      {...props}
    >
      {(props) => <EmojiList {...props} />}
    </SuggestionMenu>
  );
};

const getSuggestionItems = async (props: { query: string; editor: Editor }) => {
  const emojis: EmojiItem[] =
    (await props.editor.extensionStorage.emoji.emojis) || [];
  const filteredEmojis = getFilteredEmojis({ query: props.query, emojis });

  return filteredEmojis.map(
    (emoji): SuggestionItem => ({
      title: emoji.name,
      subtext: emoji.shortcodes.join(', '),
      context: emoji,
      onSelect: (props: {
        editor: Editor;
        range: Range;
        context?: EmojiItem;
      }) => {
        if (!(props.editor && props.range && props.context)) return;
        props.editor.chain().focus().setEmoji(props.context.name).run();
      },
    })
  );
};

const EmojiList = ({
  items,
  selectedIndex,
  onSelect,
}: SuggestionMenuRenderProps<EmojiItem>) => {
  const renderedItems = React.useMemo(() => {
    const rendered: React.ReactElement[] = [];

    items.forEach((item, index) => {
      if (!item.context) return;

      rendered.push(
        <EmojiMenuItem
          emoji={item.context}
          index={index}
          isSelected={index === selectedIndex}
          key={item.title}
          onSelect={() => onSelect(item)}
          selector="[data-selector='tiptap-emoji-dropdown-menu']"
        />
      );
    });

    return rendered;
  }, [items, selectedIndex, onSelect]);

  if (!renderedItems.length) {
    return null;
  }

  return (
    <Card
      style={{
        maxHeight: 'var(--suggestion-menu-max-height)',
      }}
    >
      <CardBody>
        <ButtonGroup>{renderedItems}</ButtonGroup>
      </CardBody>
    </Card>
  );
};
