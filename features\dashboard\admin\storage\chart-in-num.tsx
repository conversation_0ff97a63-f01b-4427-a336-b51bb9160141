'use client';

// import { useQuery } from 'convex/react';
import { useQuery } from 'convex-helpers/react/cache/hooks';
import React from 'react';
import { Label, Pie, PieChart } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { api } from '@/convex/_generated/api';

const chartConfig = {
  total: {
    label: 'Total',
  },
  images: {
    label: 'Images',
    color: 'var(--chart-1)',
  },
  videos: {
    label: 'Videos',
    color: 'var(--chart-2)',
  },
  audios: {
    label: 'Audios',
    color: 'var(--chart-3)',
  },
  documents: {
    label: 'Documents',
    color: 'var(--chart-4)',
  },
  others: {
    label: 'Others',
    color: 'var(--chart-5)',
  },
} satisfies ChartConfig;

export function StorageChartInNumber() {
  const chartData = useQuery(api.admin.getMediaStats);

  const totalFiles = React.useMemo(() => {
    return chartData?.reduce((acc, curr) => acc + curr.total, 0) || 0;
  }, [chartData]);

  if (!chartData) return null;

  return (
    <Card className="flex w-full flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Storage Usage in Number of Files</CardTitle>
        <CardDescription>All files stored in the system</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          className="mx-auto aspect-square max-h-[250px]"
          config={chartConfig}
        >
          <PieChart>
            <ChartTooltip
              content={<ChartTooltipContent hideLabel />}
              cursor={false}
            />
            <Pie
              data={chartData}
              dataKey="total"
              innerRadius={60}
              nameKey="fileType"
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text
                        dominantBaseline="middle"
                        textAnchor="middle"
                        x={viewBox.cx}
                        y={viewBox.cy}
                      >
                        <tspan
                          className="fill-foreground font-bold text-3xl"
                          x={viewBox.cx}
                          y={viewBox.cy}
                        >
                          {totalFiles.toLocaleString()}
                        </tspan>
                        <tspan
                          className="fill-muted-foreground"
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                        >
                          Files
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="max-w-sm text-center text-muted-foreground leading-none">
          <p className="hidden md:block">
            This chart summarizes the total number of files stored in the
            system, categorized by type.
          </p>
          <p className="md:hidden">Total media files uploaded.</p>
        </div>
      </CardFooter>
    </Card>
  );
}
