'use client';
import type { FunctionReturnType } from 'convex/server';
import { DataTable } from '@/components/custom/data-table';
import type { api } from '@/convex/_generated/api';

import { MediaColumns } from './columns';

export type TAllMedia = FunctionReturnType<typeof api.media.listMediaFiles>;

export default function MediaTable({ medias }: { medias: TAllMedia }) {
  if (!(medias && Array.isArray(medias))) {
    return null;
  }
  // Handle empty media state
  if (medias.length === 0) {
    return (
      <div className="p-4 text-muted-foreground">
        No media found. Create your first media!
      </div>
    );
  }

  return (
    <div className=" rounded-md bg-background p-4">
      <DataTable columns={MediaColumns} data={medias} />
    </div>
  );
}
