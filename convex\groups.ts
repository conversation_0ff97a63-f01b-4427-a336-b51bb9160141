import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { requireUser } from "./users";
import { api } from "./_generated/api";

export const createGroup = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== "admin") {
        return { success: false, error: "Unauthorized." };
      }

      const groupId = await ctx.db.insert("groups", {
        ...args,
        status: "approved",
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "group",
          docId: groupId,
        },
        docTitle: args.name,
        docStatus: "approved",
        action: "created" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to create group." };
    }
  },
});

export const updateGroup = mutation({
  args: {
    id: v.id("groups"),
    name: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== "admin") {
        return { success: false, error: "Unauthorized." };
      }

      await ctx.db.patch(args.id, {
        name: args.name,
        description: args.description,
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "group",
          docId: args.id,
        },
        docTitle: args.name,
        docStatus: "approved",
        action: "updated" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to update group." };
    }
  },
});
export const getGroups = query({
  args: {},
  handler: async (ctx) => {
    try {
      const groups = await ctx.db.query("groups").collect();
      return groups;
    } catch {
      return { success: false, error: "Failed to fetch groups." };
    }
  },
});

export const getGroup = query({
  args: {
    id: v.id("groups"),
  },
  handler: async (ctx, args) => {
    try {
      const group = await ctx.db.get(args.id);
      return group;
    } catch {
      return { success: false, error: "Failed to fetch group." };
    }
  },
});

export const searchGroups = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const groups = await ctx.db
        .query("groups")
        .withSearchIndex("search_name", (q) => q.search("name", query))
        .collect();
      return groups;
    } catch {
      return { success: false, error: "Failed to search groups." };
    }
  },
});

export const countArticlesInGroup = query({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      const count = await ctx.db
        .query("articles")
        .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
        .collect()
        .then((articles) => articles.length);
      return count;
    } catch {
      return { success: false, error: "Failed to fetch articles." };
    }
  },
});

export const countMediaInGroup = query({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      const count = await ctx.db
        .query("mediaFiles")
        .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
        .collect()
        .then((media) => media.length);
      return count;
    } catch {
      return { success: false, error: "Failed to fetch media." };
    }
  },
});
export const articlesInGroup = query({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      const articles = await ctx.db
        .query("articles")
        .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
        .collect();
      return articles;
    } catch {
      return { success: false, error: "Failed to fetch articles." };
    }
  },
});

export const mediaInGroup = query({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      const media = await ctx.db
        .query("mediaFiles")
        .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
        .collect();
      return media;
    } catch {
      return { success: false, error: "Failed to fetch media." };
    }
  },
});

// when i delete group remove id on articles and media
export const deleteGroup = mutation({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      // const articles = await ctx.db
      //   .query("articles")
      //   .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
      //   .collect();
      // for (const article of articles) {
      //   await ctx.db.patch(article._id, { groupId: undefined });
      // }
      // const media = await ctx.db
      //   .query("mediaDocs")
      //   .withIndex("by_groupId", (q) => q.eq("groupId", groupId))
      //   .collect();
      // for (const item of media) {
      //   await ctx.db.patch(item._id, { groupId: undefined });
      // }
      await ctx.db.patch(groupId, { status: "deleted" });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "group",
          docId: groupId,
        },
        docTitle: "Group",
        docStatus: "deleted",
        action: "deleted" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete group." };
    }
  },
});

export const restoreGroup = mutation({
  args: {
    groupId: v.id("groups"),
  },
  handler: async (ctx, { groupId }) => {
    try {
      await ctx.db.patch(groupId, { status: "approved" });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "group",
          docId: groupId,
        },
        docTitle: "Group",
        docStatus: "approved",
        action: "restored" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to restore group." };
    }
  },
});

// add group to article
export const addGroupToArticle = mutation({
  args: {
    articleId: v.id("articles"),
    groupId: v.id("groups"),
  },
  handler: async (ctx, { articleId, groupId }) => {
    try {
      await ctx.db.patch(articleId, { groupId });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: "Article",
        docStatus: "approved",
        action: "group-added" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to add group to article." };
    }
  },
});

export const removeGroupFromArticle = mutation({
  args: {
    articleId: v.id("articles"),
  },
  handler: async (ctx, { articleId }) => {
    try {
      await ctx.db.patch(articleId, { groupId: undefined });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: "Article",
        docStatus: "approved",
        action: "group-removed" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to remove group from article." };
    }
  },
});

export const addGroupToMedia = mutation({
  args: {
    mediaId: v.id("mediaFiles"),
    groupId: v.id("groups"),
  },
  handler: async (ctx, { mediaId, groupId }) => {
    try {
      await ctx.db.patch(mediaId, { groupId });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: mediaId,
        },
        docTitle: "Media",
        docStatus: "approved",
        action: "group-added" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to add group to media." };
    }
  },
});

export const removeGroupFromMedia = mutation({
  args: {
    mediaId: v.id("mediaFiles"),
  },
  handler: async (ctx, { mediaId }) => {
    try {
      await ctx.db.patch(mediaId, { groupId: undefined });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: mediaId,
        },
        docTitle: "Media",
        docStatus: "approved",
        action: "group-removed" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to remove group from media." };
    }
  },
});
