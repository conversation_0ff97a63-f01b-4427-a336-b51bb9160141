"use client";

import { NodeSelection, TextSelection } from "@tiptap/pm/state";
import type { Editor } from "@tiptap/react";
import React from "react";
import { useHotkeys } from "react-hotkeys-hook";
// --- Icons ---
import { TypeIcon } from "@/components/tiptap-icons/type-icon";
import { useIsMobile } from "@/hooks/use-mobile";
// --- Hooks ---
import { useTiptapEditor } from "@/hooks/use-tiptap-editor";

// --- Lib ---
import {
  findNodePosition,
  isNodeInSchema,
  isNodeTypeSelected,
  isValidPosition,
} from "@/lib/tiptap-utils";

export const TEXT_SHORTCUT_KEY = "mod+alt+0";

/**
 * Configuration for the text/paragraph functionality
 */
export interface UseTextConfig {
  /**
   * The Tiptap editor instance.
   */
  editor?: Editor | null;
  /**
   * Whether the button should hide when text conversion is not available.
   * @default false
   */
  hideWhenUnavailable?: boolean;
  /**
   * Callback function called after a successful conversion.
   */
  onToggled?: () => void;
}

/**
 * Checks if text/paragraph conversion can be performed in the current editor state
 */
export function canToggleText(editor: Editor | null, turnInto = true): boolean {
  if (!editor) return false;
  if (
    !isNodeInSchema("paragraph", editor) ||
    isNodeTypeSelected(editor, ["image"])
  )
    return false;

  if (!turnInto) {
    return editor.can().setNode("paragraph");
  }

  try {
    const view = editor.view;
    const state = view.state;
    const selection = state.selection;

    if (selection.empty || selection instanceof TextSelection) {
      const pos = findNodePosition({
        editor,
        node: state.selection.$anchor.node(1),
      })?.pos;
      if (!isValidPosition(pos)) return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Checks if paragraph is currently active
 */
export function isParagraphActive(editor: Editor | null): boolean {
  if (!editor) return false;
  return editor.isActive("paragraph");
}

/**
 * Converts the current selection or node to paragraph
 */
export function toggleParagraph(editor: Editor | null): boolean {
  if (!(editor && editor.isEditable)) return false;
  if (!canToggleText(editor)) return false;

  try {
    const view = editor.view;
    let state = view.state;
    let tr = state.tr;

    // No selection, find the the cursor position
    if (state.selection.empty || state.selection instanceof TextSelection) {
      const pos = findNodePosition({
        editor,
        node: state.selection.$anchor.node(1),
      })?.pos;
      if (!isValidPosition(pos)) return false;

      tr = tr.setSelection(NodeSelection.create(state.doc, pos));
      view.dispatch(tr);
      state = view.state;
    }

    const selection = state.selection;
    let chain = editor.chain().focus();

    // Handle NodeSelection
    if (selection instanceof NodeSelection) {
      const firstChild = selection.node.firstChild?.firstChild;
      const lastChild = selection.node.lastChild?.lastChild;

      const from = firstChild
        ? selection.from + firstChild.nodeSize
        : selection.from + 1;

      const to = lastChild
        ? selection.to - lastChild.nodeSize
        : selection.to - 1;

      chain = chain.setTextSelection({ from, to }).clearNodes();
    }

    if (!editor.isActive("paragraph")) {
      chain.setNode("paragraph").run();
    }

    editor.chain().focus().selectTextblockEnd().run();

    return true;
  } catch {
    return false;
  }
}

/**
 * Determines if the text button should be shown
 */
export function shouldShowButton(props: {
  editor: Editor | null;
  hideWhenUnavailable: boolean;
}): boolean {
  const { editor, hideWhenUnavailable } = props;

  if (!(editor && editor.isEditable)) return false;
  if (!isNodeInSchema("paragraph", editor)) return false;

  if (hideWhenUnavailable && !editor.isActive("code")) {
    return canToggleText(editor);
  }

  return true;
}

/**
 * Custom hook that provides text/paragraph functionality for Tiptap editor
 *
 * @example
 * ```tsx
 * // Simple usage - no params needed
 * function MySimpleTextButton() {
 *   const { isVisible, handleToggle, isActive } = useText()
 *
 *   if (!isVisible) return null
 *
 *   return <button onClick={handleToggle}>Text</button>
 * }
 *
 * // Advanced usage with configuration
 * function MyAdvancedTextButton() {
 *   const { isVisible, handleToggle, label, isActive } = useText({
 *     editor: myEditor,
 *     hideWhenUnavailable: true,
 *     onToggled: () => console.log('Text converted!')
 *   })
 *
 *   if (!isVisible) return null
 *
 *   return (
 *     <MyButton
 *       onClick={handleToggle}
 *       aria-label={label}
 *       aria-pressed={isActive}
 *     >
 *       Convert to Text
 *     </MyButton>
 *   )
 * }
 * ```
 */
export function useText(config?: UseTextConfig) {
  const {
    editor: providedEditor,
    hideWhenUnavailable = false,
    onToggled,
  } = config || {};

  const { editor } = useTiptapEditor(providedEditor);
  const isMobile = useIsMobile();
  const [isVisible, setIsVisible] = React.useState<boolean>(true);
  const canToggle = canToggleText(editor);
  const isActive = isParagraphActive(editor);

  React.useEffect(() => {
    if (!editor) return;

    const handleSelectionUpdate = () => {
      setIsVisible(shouldShowButton({ editor, hideWhenUnavailable }));
    };

    handleSelectionUpdate();

    editor.on("selectionUpdate", handleSelectionUpdate);

    return () => {
      editor.off("selectionUpdate", handleSelectionUpdate);
    };
  }, [editor, hideWhenUnavailable]);

  const handleToggle = React.useCallback(() => {
    if (!editor) return false;

    const success = toggleParagraph(editor);
    if (success) {
      onToggled?.();
    }
    return success;
  }, [editor, onToggled]);

  useHotkeys(
    TEXT_SHORTCUT_KEY,
    (event) => {
      event.preventDefault();
      handleToggle();
    },
    {
      enabled: isVisible && canToggle,
      enableOnContentEditable: !isMobile,
      enableOnFormTags: true,
    }
  );

  return {
    isVisible,
    isActive,
    handleToggle,
    canToggle,
    label: "Text",
    shortcutKeys: TEXT_SHORTCUT_KEY,
    Icon: TypeIcon,
  };
}
