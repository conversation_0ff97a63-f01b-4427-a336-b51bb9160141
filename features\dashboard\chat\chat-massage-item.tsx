'use client';

import { useMutation } from 'convex/react';
import type { FunctionReturnType } from 'convex/server';
import { format } from 'date-fns';
import {
  EllipsisVerticalIcon,
  PenToolIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import { cn } from '@/lib/utils';
import { EditMessageForm } from './edit-message';

export type TMessage = FunctionReturnType<typeof api.chat.getConversation>[0];

interface ChatMessageItemProps {
  message: TMessage;
  isOwnMessage: boolean;
  showHeader: boolean;
}

export const ChatMessageItem = ({
  message,
  isOwnMessage,
  showHeader,
}: ChatMessageItemProps) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const deleteMessage = useMutation(api.chat.deleteMessage);
  const undoDeleteMessage = useMutation(api.chat.undoDeleteMessage);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleDelete = async () => {
    try {
      setIsProcessing(true);
      await deleteMessage({ messageId: message._id });
      toast.success('Message deleted.');
    } catch {
      toast.error('Could not delete message.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUndoDelete = async () => {
    try {
      setIsProcessing(true);
      await undoDeleteMessage({ messageId: message._id });
      toast.success('Message restored.');
    } catch {
      toast.error('Could not restore message.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <div
        className={cn(
          'mt-2 flex',
          isOwnMessage ? 'justify-end' : 'justify-start'
        )}
      >
        <div
          className={cn('flex w-fit max-w-[75%] flex-col gap-1', {
            'items-end': isOwnMessage,
          })}
        >
          {showHeader && (
            <div
              className={cn('flex items-center gap-2 px-3 text-xs', {
                'flex-row-reverse justify-end': isOwnMessage,
              })}
            >
              {isOwnMessage && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      className="size-7 p-0"
                      disabled={isProcessing}
                      size="icon"
                      variant="ghost"
                    >
                      <EllipsisVerticalIcon className="size-4 text-muted-foreground" />
                      <span className="sr-only">Open message actions</span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="-right-11 absolute w-56 px-2 py-4">
                    <div className="flex flex-col">
                      <Button
                        className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                        onClick={
                          message.isDeleted ? handleUndoDelete : handleDelete
                        }
                        variant="ghost"
                      >
                        {message.isDeleted ? 'Restore' : 'Delete'}
                        {message.isDeleted ? (
                          <UndoIcon className="size-4 text-muted-foreground" />
                        ) : (
                          <TrashIcon className="size-4 text-muted-foreground" />
                        )}
                      </Button>
                      <Button
                        className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                        onClick={() => setOpenEditDialog(true)}
                        variant="ghost"
                      >
                        <span>Edit</span>
                        <PenToolIcon className="size-4 text-muted-foreground" />
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              )}

              <span className="font-medium">
                {isOwnMessage ? 'You' : message.senderName}
              </span>
              <span className="text-foreground/50 text-xs">
                {format(
                  new Date(message.updatedAt || message._creationTime),
                  'hh:mm a'
                )}
              </span>
            </div>
          )}

          <div
            className={cn(
              'w-fit break-words rounded-xl px-3 py-2 text-sm',
              isOwnMessage
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted text-foreground',
              message.isDeleted && 'bg-transparent '
            )}
          >
            {message.isDeleted ? (
              <p className="text-muted-foreground italic">
                This message was deleted.
              </p>
            ) : (
              <p>{message.content}</p>
            )}
          </div>
        </div>
      </div>
      <EditMessageForm
        message={message}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
      />
    </>
  );
};
