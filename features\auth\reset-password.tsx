'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Info } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { PasswordInput } from '@/components/custom/password-input';
import { Spinner } from '@/components/custom/spinner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { resetPasswordSchema, type TresetPasswordSchema } from './schema';

const UPPERCASE_REGEX = /[A-Z]/;
const LOWERCASE_REGEX = /[a-z]/;
const DIGIT_REGEX = /\d/;
const SYMBOL_REGEX = /[^A-Za-z0-9]/;
export default function ResetPasswordForm() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const { signIn } = useAuthActions();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [flow] = useState<'reset-verification' | 'signUp'>(
    'reset-verification'
  );

  const form = useForm<TresetPasswordSchema>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      pin: '',
      new_password: '',
      confirm_password: '',
    },
  });

  const calculatePasswordStrength = useCallback((password: string) => {
    let strength = 0;

    // Length-based scoring
    if (password.length >= 8) {
      strength += 20;
    }
    if (password.length >= 12) {
      strength += 20;
    }
    if (password.length >= 16) {
      strength += 20;
    }

    // Character variety
    if (UPPERCASE_REGEX.test(password)) {
      strength += 15;
    }
    if (LOWERCASE_REGEX.test(password)) {
      strength += 15;
    }
    if (DIGIT_REGEX.test(password)) {
      strength += 15;
    }
    if (SYMBOL_REGEX.test(password)) {
      strength += 15; // any symbol
    }

    return Math.min(strength, 100);
  }, []);

  const getStrengthBarColor = (strength: number) => {
    if (strength >= 85) {
      return 'bg-green-500';
    }
    if (strength >= 60) {
      return 'bg-yellow-500';
    }
    if (strength > 0) {
      return 'bg-red-500';
    }
    return 'bg-gray-300';
  };

  const handleSubmit = async (values: TresetPasswordSchema) => {
    try {
      setIsLoading(true);
      if (!email) {
        toast.error('Email is required.');
        return;
      }

      //  add reset password logic
      await signIn('password', {
        flow,
        code: values.pin,
        newPassword: values.new_password,
        email,
        redirectTo: '/sign-in',
      });
      form.reset();
      router.push('/sign-in');
      toast('Your password has been reset.');
    } catch {
      toast('An error occurred while resetting your password.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="flex flex-col gap-8">
      <header className="flex flex-col gap-2">
        <h1 className="font-bold text-2xl">Reset Password</h1>
        <p className="text-muted-foreground text-sm">
          Enter a new password and confirm it to reset your account.
        </p>
      </header>

      <Form {...form}>
        <form className="grid gap-6" onSubmit={form.handleSubmit(handleSubmit)}>
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <InputOTP maxLength={8} {...field}>
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                      <InputOTPSlot index={6} />
                      <InputOTPSlot index={7} />
                    </InputOTPGroup>
                  </InputOTP>
                </FormControl>
                <FormDescription>
                  Please enter the code sent to your email.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="new_password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>Password</FormLabel>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 cursor-pointer text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-sm">
                        At least 6 characters, including uppercase, lowercase, a
                        number, and a special character.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <FormControl>
                  <PasswordInput
                    className="rounded-none"
                    placeholder="New password"
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(e);
                      setPasswordStrength(calculatePasswordStrength(value));
                    }}
                  />
                </FormControl>
                <Progress
                  className={`mt-2 h-1 ${getStrengthBarColor(
                    passwordStrength
                  )}`}
                  value={passwordStrength}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirm_password"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <PasswordInput
                    className="rounded-none"
                    placeholder="Confirm password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            className="w-full rounded-none"
            disabled={isLoading}
            type="submit"
          >
            {isLoading ? <Spinner text="Resetting..." /> : 'Reset Password'}
          </Button>
        </form>
      </Form>
    </section>
  );
}
