{
  "$schema": "https://biomejs.dev/schemas/2.1.2/schema.json",
  "extends": ["ultracite"],
  "files": {
    "ignoreUnknown": false,
    "includes": ["**", "!**/convex/**"]
  },
  "formatter": {
    "includes": ["**", "!**/convex/**"]
  },
  "linter": {
    "rules": {
      "performance": {
        "noNamespaceImport": "warn"
      },

      "suspicious": {
        "noArrayIndexKey": "warn"
      },
      "style": {
        "noNestedTernary": "off",
        "useBlockStatements": "off"
      }
    },
    "includes": ["**", "!**/convex/**"]
  },
  "assist": {
    "includes": ["**", "!**/convex/**"]
  }
}

// biome.jsonc
// {
//   "$schema": "https://biomejs.dev/schemas/2.1.2/schema.json",
//   "extends": ["ultracite"],
//   "files": {
//     "ignoreUnknown": false,
//     "includes": [
//       "**",
//       "!**/convex/**"
//     ]
//   },
//   "formatter": {
//     "includes": [
//       "**",
//       "!**/convex/**"
//     ]
//   },
//   "linter": {
//     "includes": [
//       "**",
//       "!**/convex/**"
//     ]
//   },
//   "assist": {
//     "includes": [
//       "**",
//       "!**/convex/**"
//     ]
//   }
// }
