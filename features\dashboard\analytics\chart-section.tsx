'use client';

import { useState } from 'react';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  type ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { ChartDataPoint } from './author/data';

const chartConfig = {
  visitors: {
    label: 'Visitors',
  },

  draft: {
    label: 'Draft',
    color: 'var(--chart-5)',
  },
  staged: {
    label: 'Staged',
    color: 'var(--chart-2)',
  },
  approved: {
    label: 'Approved',
    color: 'var(--chart-3)',
  },
  published: {
    label: 'Published',
    color: 'var(--chart-4)',
  },
  deleted: {
    label: 'Deleted',
    color: 'var(--chart-1)',
  },
} satisfies ChartConfig;

export function ChartSection({
  chartData,
  title,
  description,
}: {
  chartData: ChartDataPoint[];
  title: string;
  description: string;
}) {
  const [timeRange, setTimeRange] = useState('90d');

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date);
    const referenceDate = new Date();
    let daysToSubtract = 90;
    if (timeRange === '30d') {
      daysToSubtract = 30;
    } else if (timeRange === '7d') {
      daysToSubtract = 7;
    }
    const startDate = new Date(referenceDate);
    startDate.setDate(startDate.getDate() - daysToSubtract);
    return date >= startDate;
  });

  return (
    <Card className="pt-0 shadow-none">
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1">
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <Select onValueChange={setTimeRange} value={timeRange}>
          <SelectTrigger
            aria-label="Select a value"
            className="hidden w-[160px] rounded-lg sm:ml-auto sm:flex"
          >
            <SelectValue placeholder="Last 3 months" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem className="rounded-lg" value="90d">
              Last 3 months
            </SelectItem>
            <SelectItem className="rounded-lg" value="30d">
              Last 30 days
            </SelectItem>
            <SelectItem className="rounded-lg" value="7d">
              Last 7 days
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          className="aspect-auto h-[250px] w-full"
          config={chartConfig}
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillDraft" x1="0" x2="0" y1="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-draft)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-draft)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillStaged" x1="0" x2="0" y1="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-staged)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-staged)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillApproved" x1="0" x2="0" y1="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-approved)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-approved)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillPublished" x1="0" x2="0" y1="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-published)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-published)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillDeleted" x1="0" x2="0" y1="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-deleted)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-deleted)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              axisLine={false}
              dataKey="date"
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                });
              }}
              tickLine={false}
              tickMargin={8}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                    });
                  }}
                />
              }
              cursor={false}
            />

            <Area
              dataKey="draft"
              fill="url(#fillDraft)"
              stackId="a"
              stroke="var(--color-draft)"
              type="natural"
            />
            <Area
              dataKey="staged"
              fill="url(#fillStaged)"
              stackId="a"
              stroke="var(--color-staged)"
              type="natural"
            />
            <Area
              dataKey="approved"
              fill="url(#fillApproved)"
              stackId="a"
              stroke="var(--color-approved)"
              type="natural"
            />
            <Area
              dataKey="published"
              fill="url(#fillPublished)"
              stackId="a"
              stroke="var(--color-published)"
              type="natural"
            />
            <Area
              dataKey="deleted"
              fill="url(#fillDeleted)"
              stackId="a"
              stroke="var(--color-deleted)"
              type="natural"
            />
            <ChartLegend
              content={<ChartLegendContent className="flex-wrap" />}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
