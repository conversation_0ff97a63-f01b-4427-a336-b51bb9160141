import { TabsView } from '@/components/custom/tabs-view';
import ArticleAnalyticsSection from './article-section';
import MediaAnalyticsSection from './media-section';
import StaffAnalyticsSection from './staff-section';

export default function AnalyticsTabs() {
  const tabs = [
    {
      value: 'articles',
      label: 'Articles',
      component: <ArticleAnalyticsSection />,
    },
    {
      value: 'media',
      label: 'Media',
      component: <MediaAnalyticsSection />,
    },
    {
      value: 'staff',
      label: 'Staff',
      component: <StaffAnalyticsSection />,
    },
  ];

  return <TabsView defaultValue="articles" tabs={tabs} />;
}
