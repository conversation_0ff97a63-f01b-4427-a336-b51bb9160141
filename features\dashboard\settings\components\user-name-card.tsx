'use client';
import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import { type TUsernameFormValues, usernameFormSchema } from '../schema';

export function UserNameCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const updateUsername = useMutation(api.users.updateUsername);
  const form = useForm<TUsernameFormValues>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(usernameFormSchema),
    defaultValues: { username: user?.username || '' },
  });

  useEffect(() => {
    if (user?.username) {
      form.reset({ username: user.username });
    }
  }, [user?.username, form]);

  if (!user) {
    return null;
  }
  const handleSubmit = (values: TUsernameFormValues) => {
    try {
      updateUsername({ username: values.username });
      toast.success('Username updated successfully!');
    } catch {
      toast.error('Failed to update username.');
    }
  };
  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">Username</h2>
            <p className="font-normal text-primary/60 text-sm">
              This is your username. It will be displayed on your profile.
            </p>
          </div>
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">UserName</FormLabel>
                <FormControl>
                  <div className="flex">
                    <span className="hidden items-center rounded-s-sm border border-input bg-background px-3 text-muted-foreground text-sm md:inline-flex">
                      better-flow.com/
                    </span>
                    <Input
                      autoComplete="off"
                      className="-ms-px w-fit rounded-sm rounded-s-none border-l-0 lg:min-w-2xs dark:bg-black"
                      placeholder="Username"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            Please use 48 characters at maximum.
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
