'use client';

import type { Editor } from '@tiptap/react';
import React from 'react';
// import { AiSparklesIcon } from "@/components/tiptap-icons/ai-sparkles-icon";
import { AtSignIcon } from '@/components/tiptap-icons/at-sign-icon';
import { BlockquoteIcon } from '@/components/tiptap-icons/blockquote-icon';
// --- Icons ---
import { CodeBlockIcon } from '@/components/tiptap-icons/code-block-icon';
import { HeadingOneIcon } from '@/components/tiptap-icons/heading-one-icon';
import { HeadingThreeIcon } from '@/components/tiptap-icons/heading-three-icon';
import { HeadingTwoIcon } from '@/components/tiptap-icons/heading-two-icon';
import { ImageIcon } from '@/components/tiptap-icons/image-icon';
import { ListIcon } from '@/components/tiptap-icons/list-icon';
import { ListOrderedIcon } from '@/components/tiptap-icons/list-ordered-icon';
import { ListTodoIcon } from '@/components/tiptap-icons/list-todo-icon';
import { MinusIcon } from '@/components/tiptap-icons/minus-icon';
import { SmilePlusIcon } from '@/components/tiptap-icons/smile-plus-icon';
import { TypeIcon } from '@/components/tiptap-icons/type-icon';
import { addEmojiTrigger } from '@/components/tiptap-ui/emoji-trigger-button';
import { addMentionTrigger } from '@/components/tiptap-ui/mention-trigger-button';

// --- Tiptap UI ---
import type { SuggestionItem } from '@/components/tiptap-ui-utils/suggestion-menu';
import { Button } from '@/components/ui/button';

// --- UI Primitives for image dialog ---
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// import {
//   findSelectionPosition,
//   hasContentAbove,
// } from "@/lib/tiptap-advanced-utils";
// --- Lib ---
import { isExtensionAvailable, isNodeInSchema } from '@/lib/tiptap-utils';

export interface SlashMenuConfig {
  enabledItems?: SlashMenuItemType[];
  customItems?: SuggestionItem[];
  itemGroups?: {
    [key in SlashMenuItemType]?: string;
  };
  showGroups?: boolean;
}

// Image URL Dialog Component
// Image URL Dialog Component
const ImageUrlDialog = ({
  editor,
  open,
  onOpenChange,
}: {
  editor: Editor;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const [imageUrl, setImageUrl] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleUpload = () => {
    if (!imageUrl.trim()) return;

    setIsLoading(true);
    try {
      // Insert the image into the editor
      editor.chain().focus().setImage({ src: imageUrl.trim() }).run();

      // Reset and close dialog
      setImageUrl('');
      onOpenChange(false);
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleUpload();
    }
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Image</DialogTitle>
          <DialogDescription>
            Enter the URL of the image you want to insert.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="imageUrl">Image URL</Label>
            <Input
              autoFocus
              id="imageUrl"
              onChange={(e) => setImageUrl(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="https://example.com/image.jpg"
              type="url"
              value={imageUrl}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={() => onOpenChange(false)}
            type="button"
            variant="outline"
          >
            Cancel
          </Button>
          <Button
            disabled={!imageUrl.trim() || isLoading}
            onClick={handleUpload}
            type="button"
          >
            {isLoading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const texts = {
  // Style
  text: {
    title: 'Text',
    subtext: 'Regular text paragraph',
    aliases: ['p', 'paragraph', 'text'],
    badge: TypeIcon,
    group: 'Style',
  },
  heading_1: {
    title: 'Heading 1',
    subtext: 'Top-level heading',
    aliases: ['h', 'heading1', 'h1'],
    badge: HeadingOneIcon,
    group: 'Style',
  },
  heading_2: {
    title: 'Heading 2',
    subtext: 'Key section heading',
    aliases: ['h2', 'heading2', 'subheading'],
    badge: HeadingTwoIcon,
    group: 'Style',
  },
  heading_3: {
    title: 'Heading 3',
    subtext: 'Subsection and group heading',
    aliases: ['h3', 'heading3', 'subheading'],
    badge: HeadingThreeIcon,
    group: 'Style',
  },
  bullet_list: {
    title: 'Bullet List',
    subtext: 'List with unordered items',
    aliases: ['ul', 'li', 'list', 'bulletlist', 'bullet list'],
    badge: ListIcon,
    group: 'Style',
  },
  ordered_list: {
    title: 'Numbered List',
    subtext: 'List with ordered items',
    aliases: ['ol', 'li', 'list', 'numberedlist', 'numbered list'],
    badge: ListOrderedIcon,
    group: 'Style',
  },
  task_list: {
    title: 'To-do list',
    subtext: 'List with tasks',
    aliases: ['tasklist', 'task list', 'todo', 'checklist'],
    badge: ListTodoIcon,
    group: 'Style',
  },
  quote: {
    title: 'Blockquote',
    subtext: 'Blockquote block',
    aliases: ['quote', 'blockquote'],
    badge: BlockquoteIcon,
    group: 'Style',
  },
  code_block: {
    title: 'Code Block',
    subtext: 'Code block with syntax highlighting',
    aliases: ['code', 'pre'],
    badge: CodeBlockIcon,
    group: 'Style',
  },

  // Insert
  mention: {
    title: 'Mention',
    subtext: 'Mention a user or item',
    aliases: ['mention', 'user', 'item', 'tag'],
    badge: AtSignIcon,
    group: 'Insert',
  },
  emoji: {
    title: 'Emoji',
    subtext: 'Insert an emoji',
    aliases: ['emoji', 'emoticon', 'smiley'],
    badge: SmilePlusIcon,
    group: 'Insert',
  },
  divider: {
    title: 'Separator',
    subtext: 'Horizontal line to separate content',
    aliases: ['hr', 'horizontalRule', 'line', 'separator'],
    badge: MinusIcon,
    group: 'Insert',
  },

  // Upload
  image: {
    title: 'Image',
    subtext: 'Resizable image with caption',
    aliases: [
      'image',
      'imageUpload',
      'upload',
      'img',
      'picture',
      'media',
      'url',
    ],
    badge: ImageIcon,
    group: 'Upload',
  },
};

export type SlashMenuItemType = keyof typeof texts;

const getItemImplementations = (
  setImageDialogOpen: (open: boolean) => void
) => {
  return {
    text: {
      check: (editor: Editor) => isNodeInSchema('paragraph', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().setParagraph().run();
      },
    },
    heading_1: {
      check: (editor: Editor) => isNodeInSchema('heading', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleHeading({ level: 1 }).run();
      },
    },
    heading_2: {
      check: (editor: Editor) => isNodeInSchema('heading', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleHeading({ level: 2 }).run();
      },
    },
    heading_3: {
      check: (editor: Editor) => isNodeInSchema('heading', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleHeading({ level: 3 }).run();
      },
    },
    bullet_list: {
      check: (editor: Editor) => isNodeInSchema('bulletList', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleBulletList().run();
      },
    },
    ordered_list: {
      check: (editor: Editor) => isNodeInSchema('orderedList', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleOrderedList().run();
      },
    },
    task_list: {
      check: (editor: Editor) => isNodeInSchema('taskList', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleTaskList().run();
      },
    },
    quote: {
      check: (editor: Editor) => isNodeInSchema('blockquote', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleBlockquote().run();
      },
    },
    code_block: {
      check: (editor: Editor) => isNodeInSchema('codeBlock', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().toggleNode('codeBlock', 'paragraph').run();
      },
    },

    // Insert
    mention: {
      check: (editor: Editor) =>
        isExtensionAvailable(editor, ['mention', 'mentionAdvanced']),
      action: ({ editor }: { editor: Editor }) => addMentionTrigger(editor),
    },
    emoji: {
      check: (editor: Editor) =>
        isExtensionAvailable(editor, ['emoji', 'emojiPicker']),
      action: ({ editor }: { editor: Editor }) => addEmojiTrigger(editor),
    },
    divider: {
      check: (editor: Editor) => isNodeInSchema('horizontalRule', editor),
      action: ({ editor }: { editor: Editor }) => {
        editor.chain().focus().setHorizontalRule().run();
      },
    },

    // Upload
    image: {
      check: (editor: Editor) => isNodeInSchema('image', editor),
      action: ({ editor }: { editor: Editor }) => {
        // Open the image URL dialog instead of inserting directly
        setImageDialogOpen(true);
      },
      // action: ({ editor }: { editor: Editor }) => {
      //   editor
      //     .chain()
      //     .focus()
      //     .insertContent({
      //       type: 'imageUpload',
      //     })
      //     .run();
      // },
    },
    // when you click on image btn open that dialog
    // image: {
    //   check: (editor: Editor) => isNodeInSchema("image", editor),
    //   action: ({ editor }: { editor: Editor }) => {
    //     // todo: open dialog

    //     const url = window.prompt("Enter image URL");
    //     if (url) {
    //       editor.chain().focus().setImage({ src: url }).run();
    //     }
    //   },
    // },
  };
};

function organizeItemsByGroups(
  items: SuggestionItem[],
  showGroups: boolean
): SuggestionItem[] {
  if (!showGroups) {
    return items.map((item) => ({ ...item, group: '' }));
  }

  const groups: { [groupLabel: string]: SuggestionItem[] } = {};

  // Group items
  for (const item of items) {
    const groupLabel = item.group || '';
    if (!groups[groupLabel]) {
      groups[groupLabel] = [];
    }
    groups[groupLabel].push(item);
  }

  // Flatten groups in order (this maintains the visual order for keyboard navigation)
  const organizedItems: SuggestionItem[] = [];

  for (const [, groupItems] of Object.entries(groups)) {
    organizedItems.push(...groupItems);
  }

  return organizedItems;
}

/**
 * Custom hook for slash dropdown menu functionality
 */
export function useSlashDropdownMenu(config?: SlashMenuConfig) {
  const [imageDialogOpen, setImageDialogOpen] = React.useState(false);
  const [currentEditor, setCurrentEditor] = React.useState<Editor | null>(null);

  const getSlashMenuItems = React.useCallback(
    (editor: Editor) => {
      // Store the current editor for use in the dialog
      setCurrentEditor(editor);

      const items: SuggestionItem[] = [];

      const enabledItems =
        config?.enabledItems || (Object.keys(texts) as SlashMenuItemType[]);
      const showGroups = config?.showGroups !== false;

      const itemImplementations = getItemImplementations(setImageDialogOpen);

      for (const itemType of enabledItems) {
        const itemImpl =
          itemImplementations[itemType as keyof typeof itemImplementations];

        const itemText = texts[itemType];

        if (itemImpl && itemText && itemImpl.check(editor)) {
          const suggestionItem: SuggestionItem = {
            onSelect: ({ editor }) => itemImpl.action({ editor }),
            ...itemText,
          };

          if (config?.itemGroups?.[itemType]) {
            suggestionItem.group = config.itemGroups[itemType];
          } else if (!showGroups) {
            suggestionItem.group = '';
          }

          items.push(suggestionItem);
        }
      }

      if (config?.customItems) {
        items.push(...config.customItems);
      }

      // Reorganize items by groups to ensure keyboard navigation works correctly
      return organizeItemsByGroups(items, showGroups);
    },
    [config]
  );
  // Render the image dialog alongside the hook
  const ImageDialog = React.useCallback(() => {
    if (!currentEditor) return null;

    return (
      <ImageUrlDialog
        editor={currentEditor}
        onOpenChange={setImageDialogOpen}
        open={imageDialogOpen}
      />
    );
  }, [currentEditor, imageDialogOpen]);

  return {
    // getSlashMenuItems,
    // config,
    getSlashMenuItems,
    config,
    ImageDialog,
    imageDialogOpen,
    setImageDialogOpen,
  };
}
