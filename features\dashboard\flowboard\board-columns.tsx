// 'use client';

// import { useQuery } from 'convex/react';
// import { GripVertical } from 'lucide-react';
// import { useCallback, useEffect, useState } from 'react';
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import { Badge2 } from '@/components/ui/badge2';
// import { Button } from '@/components/ui/button';
// // import { Button2 } from '@/components/ui/button2';
// import {
//   Kanban,
//   KanbanBoard,
//   KanbanColumn,
//   KanbanColumnContent,
//   KanbanColumnHandle,
//   KanbanItem,
//   KanbanItemHandle,
//   KanbanOverlay,
// } from '@/components/ui/kanban';
// import { api } from '@/convex/_generated/api';
// import type { Id } from '@/convex/_generated/dataModel';
// import CreateOrUpdateTask from './create-edit-task';

// interface Task {
//   id: string;
//   title: string;
//   priority: 'low' | 'medium' | 'high';
//   description?: string;
//   assignee?: string;
//   assigneeAvatar?: string;
//   dueDate?: string;
// }

// // const COLUMN_TITLES: Record<string, string> = {
// //   backlog: 'Backlog',
// //   inProgress: 'In Progress',
// //   review: 'Review',
// //   done: 'Done',
// // };

// interface TaskCardProps
//   extends Omit<React.ComponentProps<typeof KanbanItem>, 'value' | 'children'> {
//   task: Task;
//   asHandle?: boolean;
// }

// function TaskCard({ task, asHandle, ...props }: TaskCardProps) {
//   const cardContent = (
//     <div className="rounded-md border bg-card p-3 shadow-xs">
//       <div className="flex flex-col gap-2.5">
//         <div className="flex items-center justify-between gap-2">
//           <span className="line-clamp-1 font-medium text-sm">{task.title}</span>
//           <Badge2
//             appearance="outline"
//             className="pointer-events-none h-5 shrink-0 rounded-sm px-1.5 text-[11px] capitalize"
//             variant={
//               task.priority === 'high'
//                 ? 'destructive'
//                 : task.priority === 'medium'
//                   ? 'primary'
//                   : 'warning'
//             }
//           >
//             {task.priority}
//           </Badge2>
//         </div>
//         <div className="flex items-center justify-between text-muted-foreground text-xs">
//           {task.assignee && (
//             <div className="flex items-center gap-1">
//               <Avatar className="size-4">
//                 <AvatarImage src={task.assigneeAvatar} />
//                 <AvatarFallback>{task.assignee.charAt(0)}</AvatarFallback>
//               </Avatar>
//               <span className="line-clamp-1">{task.assignee}</span>
//             </div>
//           )}
//           {task.dueDate && (
//             <time className="whitespace-nowrap text-[10px] tabular-nums">
//               {task.dueDate}
//             </time>
//           )}
//         </div>
//       </div>
//     </div>
//   );

//   return (
//     <KanbanItem value={task.id} {...props}>
//       {asHandle ? (
//         <KanbanItemHandle>{cardContent}</KanbanItemHandle>
//       ) : (
//         cardContent
//       )}
//     </KanbanItem>
//   );
// }

// interface TaskColumnProps
//   extends Omit<React.ComponentProps<typeof KanbanColumn>, 'children'> {
//   title: string;
//   tasks: Task[];
//   isOverlay?: boolean;
//   boardId: Id<'boards'>;
// }

// function TaskColumn({
//   value,
//   title,
//   tasks,
//   isOverlay,
//   boardId,
//   ...props
// }: TaskColumnProps) {
//   //   get column name from value
//   const columnData = useQuery(api.board.getColumn, {
//     id: value as Id<'boardColumns'>,
//   });
//   const columnTitle =
//     columnData && 'title' in columnData ? columnData.title : title;

//   return (
//     <KanbanColumn
//       value={value}
//       {...props}
//       className="rounded-md border bg-card p-2.5 shadow-xs"
//     >
//       <div className="mb-2.5 flex items-center justify-between">
//         <div className="flex items-center gap-2.5">
//           <span className="font-semibold text-sm">{columnTitle}</span>
//           <Badge2 variant="secondary">{tasks.length}</Badge2>
//         </div>
//         <KanbanColumnHandle asChild>
//           <Button size="sm" variant="dim">
//             <GripVertical />
//           </Button>
//         </KanbanColumnHandle>
//       </div>
//       <KanbanColumnContent
//         className="flex flex-col gap-2.5 p-0.5"
//         value={value}
//       >
//         {tasks.map((task) => (
//           <TaskCard asHandle={!isOverlay} key={task.id} task={task} />
//         ))}
//         {/* if no tasks add one */}
//         {tasks.length === 0 && (
//           <CreateOrUpdateTask
//             boardId={boardId}
//             columnId={value as Id<'boardColumns'>}
//           />
//         )}
//       </KanbanColumnContent>
//     </KanbanColumn>
//   );
// }

// export default function BoardColumns({ boardId }: { boardId: Id<'boards'> }) {
//   const allColumns = useQuery(api.board.getBoardData, { boardId }) || {};

//   // state just starts empty
//   const [columns, setColumns] = useState<Record<string, Task[]>>(allColumns);

//   useEffect(() => {
//     if (allColumns && !Array.isArray(allColumns)) {
//       setColumns(allColumns);
//     }
//   }, [allColumns]);

//   const handleValueChange = useCallback(
//     (newColumns: Record<string, Task[]>) => {
//       // only update if different
//       setColumns((prev) => {
//         if (JSON.stringify(prev) === JSON.stringify(newColumns)) {
//           return prev; // no state change
//         }
//         return newColumns;
//       });
//     },
//     []
//   );
//   if (!allColumns) {
//     return null;
//   }

//   return (
//     <Kanban
//       getItemValue={(item) => item.id}
//       onValueChange={handleValueChange}
//       value={columns}
//     >
//       <KanbanBoard className="grid auto-rows-fr grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
//         {Object.entries(columns).map(([columnValue, tasks]) => (
//           <TaskColumn
//             boardId={boardId}
//             key={columnValue}
//             tasks={tasks}
//             title={columnValue}
//             value={columnValue}
//           />
//         ))}
//       </KanbanBoard>
//       <KanbanOverlay>
//         <div className="size-full rounded-md bg-muted/60" />
//       </KanbanOverlay>
//     </Kanban>
//   );
// }
