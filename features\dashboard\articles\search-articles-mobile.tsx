'use client';
import { SearchIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import type { TAllArticles } from './article-table';

export default function SearchArticlesMobile({
  articles,
  basePath,
}: {
  articles: TAllArticles;
  basePath: string;
}) {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = useState('');

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'a' && (e.metaKey || e.ctrlKey)) {
        if (
          (e.target instanceof HTMLElement && e.target.isContentEditable) ||
          e.target instanceof HTMLInputElement ||
          e.target instanceof HTMLTextAreaElement ||
          e.target instanceof HTMLSelectElement
        ) {
          return;
        }

        e.preventDefault();
        setOpen((op) => !op);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const runCommand = React.useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);

  if (!(articles && Array.isArray(articles))) {
    return null;
  }
  return (
    <>
      <Button
        className="min-h-10 w-fit bg-background sm:hidden"
        onClick={() => setOpen(true)}
        variant="outline"
      >
        <SearchIcon className="size-5" />
        <span>Search</span>
        <span className="sr-only">Open find</span>
      </Button>
      <CommandDialog
        className="top-[30%] rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'Search articles...'}
        />
        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>
          </CommandEmpty>
          <CommandGroup heading="Articles">
            {articles.map((article) => (
              <CommandItem
                key={article._id}
                onSelect={() => {
                  runCommand(() =>
                    router.push(`${basePath}/editor/${article.slug}`)
                  );
                }}
                value={article.title}
              >
                {article.title}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
