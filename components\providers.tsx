'use client';

import { AppProgressProvider } from '@bprogress/next';
import { ConvexQueryCacheProvider } from 'convex-helpers/react/cache/provider';
import { ConvexClientProvider } from '@/components/convex-client-provider';
import { ThemeProvider } from '@/components/mode/theme-provider';
import { Toaster } from '@/components/ui/sonner';

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ConvexClientProvider>
      <ConvexQueryCacheProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          disableTransitionOnChange
          enableSystem
        >
          <AppProgressProvider
            color="var(--foreground)"
            delay={500}
            height="2px"
            options={{ showSpinner: false }}
          >
            {children}
          </AppProgressProvider>
          <Toaster closeButton />
        </ThemeProvider>
      </ConvexQueryCacheProvider>
    </ConvexClientProvider>
  );
}
