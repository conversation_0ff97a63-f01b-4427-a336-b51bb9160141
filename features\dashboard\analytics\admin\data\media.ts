// import { useQuery } from 'convex/react';
import { useQuery } from "convex-helpers/react/cache/hooks";
import { useMemo } from "react";
import { api } from "@/convex/_generated/api";

const statusList = [
  "draft",
  "staged",
  "approved",
  "published",
  "deleted",
] as const;

type Status = (typeof statusList)[number];

// Chart data structure for each date
export type ChartDataPoint = {
  date: string;
  draft: number;
  staged: number;
  approved: number;
  published: number;
  deleted: number;
};

export function useMediaAnalyticsChartDataByAdmin() {
  const raw = useQuery(api.admin.getMediaAnalytics) || [];
  const chartData: ChartDataPoint[] = useMemo(() => {
    const countsByDate: Record<string, ChartDataPoint> = {};

    for (const { date, status } of raw) {
      if (!countsByDate[date]) {
        countsByDate[date] = {
          date,
          draft: 0,
          staged: 0,
          approved: 0,
          published: 0,
          deleted: 0,
        };
      }

      // Type assertion to ensure status is a valid key
      countsByDate[date][status as Status]++;
    }

    // Sort by date ascending
    return Object.values(countsByDate).sort((a, b) =>
      a.date.localeCompare(b.date)
    );
  }, [raw]);

  return chartData;
}
