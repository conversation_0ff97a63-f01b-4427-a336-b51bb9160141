import { useMutation, useQuery } from 'convex/react';
import { ImageOffIcon } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { ImageIcon } from '@/components/tiptap-icons/image-icon';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { getIconByMediaType } from '../shared/icons';

export default function AddCoverToArticle({
  articleId,
  prevCoverImg,
  status,
}: {
  articleId: Id<'articles'>;
  prevCoverImg: string;
  status: string;
}) {
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [externalUrl, setExternalUrl] = useState(prevCoverImg);
  const media = useQuery(api.media.listMediaFiles);
  const addMediaCoverToArticle = useMutation(api.media.addMediaCoverToArticle);
  const removeMediaCoverFromArticle = useMutation(
    api.media.removeMediaCoverFromArticle
  );
  const handleAddMediaCoverToArticle = async (
    id: Id<'articles'>,
    mediaId: Id<'mediaFiles'>
  ) => {
    try {
      await addMediaCoverToArticle({ articleId: id, mediaId });
      toast.success('Cover added successfully!');
    } catch {
      toast.error('Failed to add cover.');
    }
  };

  const handleAddExternalCoverToArticle = async (
    id: Id<'articles'>,
    url: string
  ) => {
    try {
      await addMediaCoverToArticle({ articleId: id, externalUrl: url });
      toast.success('Cover added successfully!');
    } catch {
      toast.error('Failed to add cover.');
    }
  };
  const handleRemoveMediaCoverFromArticle = async (id: Id<'articles'>) => {
    try {
      await removeMediaCoverFromArticle({ articleId: id });
      toast.success('Cover removed successfully!');
    } catch {
      toast.error('Failed to remove cover.');
    }
  };
  const runCommand = useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);
  const hasCover = Boolean(prevCoverImg);
  if (!media || 'success' in media) {
    return null;
  }
  // filter media to only show images
  const images = media.filter((file) => file.contentType === 'image');
  return (
    <>
      {hasCover ? (
        <>
          <Button
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpen(true)}
            variant="ghost"
          >
            <span>Change Cover</span>
            <ImageIcon className="size-4 text-muted-foreground" />
          </Button>
          {status !== 'published' && (
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() => handleRemoveMediaCoverFromArticle(articleId)}
              variant="ghost"
            >
              <span>Remove Cover</span>
              <ImageOffIcon className="size-4 text-muted-foreground" />
            </Button>
          )}
        </>
      ) : (
        <Button
          className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
          onClick={() => setOpen(true)}
          variant="ghost"
        >
          <span>Add Cover</span>
          <ImageIcon className="size-4 text-muted-foreground" />
        </Button>
      )}
      <CommandDialog
        className="rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'Search media...'}
        />
        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>.
          </CommandEmpty>
          <CommandGroup heading="Media">
            {images.map((file) => {
              const MediaIcon = getIconByMediaType(file.contentType);
              return (
                <CommandItem
                  key={file._id}
                  onSelect={() => {
                    runCommand(() =>
                      handleAddMediaCoverToArticle(articleId, file._id)
                    );
                  }}
                  value={file.title}
                >
                  <HoverCard>
                    <HoverCardTrigger>{file.title}</HoverCardTrigger>
                    <HoverCardContent align="end" className="ml-10 p-2">
                      <AspectRatio
                        className="rounded-lg bg-muted"
                        ratio={16 / 9}
                      >
                        {file.url ? (
                          <Image
                            alt="Photo by Drew Beamer"
                            className="h-full w-full rounded-lg object-cover dark:brightness-[0.2] dark:grayscale"
                            fill
                            src={file.url}
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                            <p className="text-sm">No preview available</p>
                          </div>
                        )}
                      </AspectRatio>
                    </HoverCardContent>
                  </HoverCard>
                  {prevCoverImg === file.url && (
                    <span className="ml-auto text-muted-foreground">
                      Current Cover
                    </span>
                  )}
                  <MediaIcon className="ml-auto size-4 text-muted-foreground" />
                </CommandItem>
              );
            })}
          </CommandGroup>
          <CommandGroup heading="External URL">
            <div className="flex items-center gap-2 px-2 py-4">
              <Input
                className="min-h-10 w-full"
                onChange={(e) => setExternalUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                value={externalUrl}
              />
              <Button
                onClick={() => {
                  runCommand(() =>
                    handleAddExternalCoverToArticle(articleId, externalUrl)
                  );
                }}
                variant="ghost"
              >
                <span>Add</span>
              </Button>
            </div>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
