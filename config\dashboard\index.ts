import {
  ArmchairIcon,
  CircleUserIcon,
  LayoutPanelTopIcon,
  LogOutIcon,
  MessageCirclePlusIcon,
} from "lucide-react";
import type { TnavItem, Tsettings } from "@/types";

export const userDropdownGroup1 = [
  {
    label: "Dashboard",
    href: "/",
    icon: LayoutPanelTopIcon,
  },
  {
    label: "Account Settings",
    href: "/settings",
    icon: CircleUserIcon,
  },
  {
    label: "Talk to Team",
    href: "/chat",
    icon: MessageCirclePlusIcon,
  },
];

export const userDropdownGroup2 = [
  {
    label: "Command Menu",
    href: "",
    custom: "command", // indicates special rendering (Ctrl + K)
  },
  {
    label: "Theme",
    href: "",
    custom: "theme", // indicates theme switcher UI
  },
];

export const userDropdownGroup3 = [
  {
    label: "Home Page",
    href: "/",
    icon: ArmchairIcon,
  },
  {
    label: "Logout",
    href: "/sign-in",
    icon: LogOutIcon,
  },
];

export const authorNavItems: TnavItem[] = [
  {
    label: "Overview",
    link: "/author",
  },
  {
    label: "Editor",
    link: "/author/editor",
  },
  {
    label: "Media",
    link: "/author/media",
  },
  {
    label: "Activity",
    link: "/author/activity",
  },
  {
    label: "Chat",
    link: "/author/chat",
  },
  {
    label: "Analytics",
    link: "/author/analytics",
  },
  {
    label: "Flowboard",
    link: "/author/flowboard",
  },
  {
    label: "Settings",
    link: "/author/settings",
  },
];
export const adminNavItems: TnavItem[] = [
  {
    label: "Overview",
    link: "/admin",
  },

  {
    label: "Articles",
    link: "/admin/articles",
  },
  {
    label: "Media",
    link: "/admin/media",
  },
  {
    label: "Ads",
    link: "/admin/ads",
    comingSoon: true,
  },
  {
    label: "Groups",
    link: "/admin/groups",
  },
  {
    label: "Activity",
    link: "/admin/activity",
  },
  {
    label: "Chat",
    link: "/admin/chat",
  },
  {
    label: "Analytics",
    link: "/admin/analytics",
  },
  {
    label: "Storage",
    link: "/admin/storage",
  },
  {
    label: "Flowboard",
    link: "/admin/flowboard",
  },
  {
    label: "Settings",
    link: "/admin/settings",
  },
];

export const mediaNavItems: TnavItem[] = [
  {
    label: "Overview",
    link: "/media-manager",
  },
  {
    label: "Upload",
    link: "/media-manager/upload",
  },
  {
    label: "Media",
    link: "/media-manager/media",
  },
  {
    label: "Activity",
    link: "/media-manager/activity",
  },
  {
    label: "Chat",
    link: "/media-manager/chat",
  },
  {
    label: "Analytics",
    link: "/media-manager/analytics",
  },
  {
    label: "Flowboard",
    link: "/media-manager/flowboard",
  },
  {
    label: "Settings",
    link: "/media-manager/settings",
  },
];

export const settingLinks: Tsettings[] = [
  {
    label: "General",
    href: "/settings",
  },
  {
    label: "Authentications",
    href: "/settings/auth",
  },
  {
    label: "Security & Privacy",
    href: "/settings/security",
    comingSoon: true,
  },
];

export const footerLinks = [
  {
    label: "Home",
    href: "/",
  },
  {
    label: "Docs",
    href: "/docs",
  },
  {
    label: "Guides",
    href: "/guide",
  },
  {
    label: "Help",
    href: "/help",
  },
  {
    label: "Contact",
    href: "/contact",
  },
  {
    label: "Legal",
    href: "/legal",
  },
];
