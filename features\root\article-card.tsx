import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import type { TArticle } from './types';

export default function ArticleCard({ article }: { article: TArticle }) {
  const { title, slug, publishedAt, coverImage, group } = article;
  return (
    <Link
      className="relative flex h-full cursor-pointer flex-col gap-4 rounded-sm bg-background md:p-2"
      href={`/article/${slug}`}
    >
      <div className="relative w-full overflow-clip rounded-md ring ring-ring/25">
        <AspectRatio
          className="group w-full cursor-pointer overflow-clip "
          ratio={16 / 9}
        >
          <Image
            alt={`Featured image for article: ${title}`}
            className="overflow-clip rounded-sm object-cover transition-transform duration-300 ease-in hover:scale-105"
            fill
            priority
            src={coverImage || ''}
          />
        </AspectRatio>
      </div>

      <h2 className="text-pretty pr-5 font-medium text-lg leading-6 tracking-tight">
        {title}
      </h2>
      <div className="mt-auto md:pr-12 ">
        <div className="mt-auto flex flex-wrap items-center gap-x-3 gap-y-2 font-medium text-sm">
          <p className="text-wrap">{group}</p>
          <p className="text-muted-foreground text-sm">
            {formatDistanceToNow(new Date(publishedAt), {
              addSuffix: true,
            })}
          </p>
        </div>
      </div>
    </Link>
  );
}
